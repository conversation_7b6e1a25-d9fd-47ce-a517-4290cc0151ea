import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { WebView } from 'react-native-webview';
import * as Location from 'expo-location';
import { Ionicons } from '@expo/vector-icons';
import { lightTheme } from '../config/theme';
import { useTheme } from '../contexts/ThemeContext';

interface LocationPickerProps {
  onLocationSelect: (location: {
    latitude: number;
    longitude: number;
    address: string;
    city: string;
    state: string;
    country: string;
  }) => void;
  initialLocation?: {
    latitude: number;
    longitude: number;
  };
  style?: any;
}

interface SearchResult {
  display_name: string;
  lat: string;
  lon: string;
  address: {
    city?: string;
    town?: string;
    village?: string;
    state?: string;
    country?: string;
  };
}

export default function LocationPicker({ onLocationSelect, initialLocation, style }: LocationPickerProps) {
  const { theme } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [selectedLocation, setSelectedLocation] = useState<{latitude: number, longitude: number} | null>(initialLocation || null);
  const [loading, setLoading] = useState(false);
  const [mapLoaded, setMapLoaded] = useState(false);
  const [webViewRef, setWebViewRef] = useState<any>(null);
  const [currentLocation, setCurrentLocation] = useState<{latitude: number, longitude: number} | null>(null);
  const [webViewError, setWebViewError] = useState<string | null>(null);

  // Initialize with initial location or fallback (no automatic GPS calls)
  useEffect(() => {
    if (initialLocation) {
      console.log('LocationPicker: Using provided initial location:', initialLocation);
      setCurrentLocation(initialLocation);
    } else if (!currentLocation) {
      console.log('LocationPicker: Using fallback location');
      setCurrentLocation({
        latitude: 20.2961, 
        longitude: 85.8245
      });
    }
  }, [initialLocation]);

  const getCurrentLocationOnLoad = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High,
        });
        const coords = {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        };
        console.log('LocationPicker: Got current location:', coords);
        setCurrentLocation(coords);
      } else {
        console.log('LocationPicker: Permission denied, using fallback location');
        // Fallback to Bhubaneswar, Odisha, India coordinates
        setCurrentLocation({
          latitude: 20.2961, 
          longitude: 85.8245
        });
      }
    } catch (error) {
      console.log('LocationPicker: Could not get current location:', error);
      // Fallback to Bhubaneswar, Odisha, India coordinates
      setCurrentLocation({
        latitude: 20.2961, 
        longitude: 85.8245
      });
    }
  };

  // Generate HTML for the map
  const generateMapHTML = (lat?: number, lng?: number) => {
    const defaultLat = lat || currentLocation?.latitude || 20.2961; // Bhubaneswar, Odisha
    const defaultLng = lng || currentLocation?.longitude || 85.8245;
    console.log('LocationPicker: Generating map HTML with coords:', { defaultLat, defaultLng, lat, lng, currentLocation });
    return `
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: https://unpkg.com https://*.openstreetmap.org https://nominatim.openstreetmap.org; connect-src https:;">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body, html { 
            margin: 0; 
            padding: 0; 
            height: 100%; 
            overflow: hidden;
            touch-action: manipulation;
        }
        #map { 
            height: 100vh; 
            width: 100%; 
            position: relative;
        }
        .current-location-btn {
            position: absolute;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            background: #007AFF;
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 20px;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            touch-action: manipulation;
        }
        .current-location-btn:active {
            background: #005cbf;
        }
        .location-info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            z-index: 1000;
            background: white;
            padding: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 250px;
            font-size: 12px;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div id="map"></div>
    <button class="current-location-btn" onclick="getCurrentLocation()" title="Get Current Location">
        📍
    </button>
    <div class="location-info" id="locationInfo" style="display: none;">
        <div id="coordinates"></div>
        <div id="address"></div>
    </div>
    
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        let map, marker;
        let currentLat = ${defaultLat};
        let currentLng = ${defaultLng};
        
        // Prevent page unload/navigation
        window.addEventListener('beforeunload', function(e) {
            e.preventDefault();
            e.returnValue = '';
        });
        
        // Prevent context menu
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });
        
        function initMap() {
            try {
                map = L.map('map', {
                    zoomControl: true,
                    scrollWheelZoom: true,
                    doubleClickZoom: true,
                    touchZoom: true,
                    dragging: true
                }).setView([currentLat, currentLng], 13);
                
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors',
                    maxZoom: 19,
                    minZoom: 3
                }).addTo(map);
                
                marker = L.marker([currentLat, currentLng], {
                    draggable: true
                }).addTo(map);
                
                marker.on('dragend', function(e) {
                    const position = e.target.getLatLng();
                    updateLocation(position.lat, position.lng);
                });
                
                map.on('click', function(e) {
                    const lat = e.latlng.lat;
                    const lng = e.latlng.lng;
                    marker.setLatLng([lat, lng]);
                    updateLocation(lat, lng);
                });
                
                // Initialize with current location
                updateLocation(currentLat, currentLng);
                
                // Ensure map container is properly sized
                setTimeout(function() {
                    map.invalidateSize();
                }, 100);
            } catch (error) {
                console.error('Map initialization error:', error);
                alert('Failed to initialize map. Please try again.');
            }
        }
        
        function updateLocation(lat, lng) {
            currentLat = lat;
            currentLng = lng;
            
            document.getElementById('coordinates').textContent = 
                'Lat: ' + lat.toFixed(6) + ', Lng: ' + lng.toFixed(6);
            document.getElementById('locationInfo').style.display = 'block';
            
            // Reverse geocoding with timeout
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 10000);
            
            fetch(\`https://nominatim.openstreetmap.org/reverse?format=json&lat=\${lat}&lon=\${lng}&addressdetails=1\`, {
                signal: controller.signal
            })
                .then(response => {
                    clearTimeout(timeoutId);
                    if (!response.ok) throw new Error('Network response was not ok');
                    return response.json();
                })
                .then(data => {
                    const address = data.display_name || 'Address not found';
                    document.getElementById('address').textContent = address;
                    
                    // Send location data to React Native
                    const locationData = {
                        latitude: lat,
                        longitude: lng,
                        address: address,
                        city: data.address?.city || data.address?.town || data.address?.village || '',
                        state: data.address?.state || '',
                        country: data.address?.country || ''
                    };
                    
                    if (window.ReactNativeWebView) {
                        window.ReactNativeWebView.postMessage(JSON.stringify({
                            type: 'locationSelected',
                            data: locationData
                        }));
                    }
                })
                .catch(error => {
                    clearTimeout(timeoutId);
                    console.error('Geocoding error:', error);
                    document.getElementById('address').textContent = 'Address lookup failed';
                    
                    // Still send location data even if geocoding fails
                    const locationData = {
                        latitude: lat,
                        longitude: lng,
                        address: \`\${lat.toFixed(6)}, \${lng.toFixed(6)}\`,
                        city: '',
                        state: '',
                        country: ''
                    };
                    
                    if (window.ReactNativeWebView) {
                        window.ReactNativeWebView.postMessage(JSON.stringify({
                            type: 'locationSelected',
                            data: locationData
                        }));
                    }
                });
        }
        
        function getCurrentLocation() {
            // Location will be handled by React Native, not the WebView
            window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'requestCurrentLocation'
            }));
        }
        
        function searchLocation(query) {
            if (!query.trim()) return;
            
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 10000);
            
            fetch(\`https://nominatim.openstreetmap.org/search?format=json&q=\${encodeURIComponent(query)}&addressdetails=1&limit=5\`, {
                signal: controller.signal
            })
                .then(response => {
                    clearTimeout(timeoutId);
                    if (!response.ok) throw new Error('Network response was not ok');
                    return response.json();
                })
                .then(data => {
                    if (window.ReactNativeWebView) {
                        window.ReactNativeWebView.postMessage(JSON.stringify({
                            type: 'searchResults',
                            data: data || []
                        }));
                    }
                })
                .catch(error => {
                    clearTimeout(timeoutId);
                    console.error('Search error:', error);
                    if (window.ReactNativeWebView) {
                        window.ReactNativeWebView.postMessage(JSON.stringify({
                            type: 'searchResults',
                            data: []
                        }));
                    }
                });
        }
        
        function goToLocation(lat, lng) {
            map.setView([lat, lng], 16);
            marker.setLatLng([lat, lng]);
            updateLocation(lat, lng);
        }
        
        // Initialize map when page loads
        initMap();
        
        // Listen for messages from React Native
        window.addEventListener('message', function(event) {
            const message = JSON.parse(event.data);
            if (message.type === 'search') {
                searchLocation(message.query);
            } else if (message.type === 'goToLocation') {
                goToLocation(message.lat, message.lng);
            }
        });
    </script>
</body>
</html>`;
  };

  const handleWebViewMessage = (event: any) => {
    try {
      const message = JSON.parse(event.nativeEvent.data);
      console.log('LocationPicker: Received WebView message:', message);
      
      if (message.type === 'locationSelected') {
        if (message.data && typeof message.data.latitude === 'number' && typeof message.data.longitude === 'number') {
          const locationData = {
            latitude: message.data.latitude,
            longitude: message.data.longitude,
          };
          console.log('LocationPicker: Setting selected location:', locationData);
          setSelectedLocation(locationData);
          onLocationSelect(message.data);
        } else {
          console.error('LocationPicker: Invalid location data received:', message.data);
        }
      } else if (message.type === 'searchResults') {
        setSearchResults(message.data || []);
        setLoading(false);
      } else if (message.type === 'requestCurrentLocation') {
        handleCurrentLocationRequest();
      }
    } catch (error) {
      console.error('LocationPicker: Error parsing WebView message:', error);
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) return;
    
    setLoading(true);
    setSearchResults([]);
    
    if (webViewRef) {
      webViewRef.postMessage(JSON.stringify({
        type: 'search',
        query: searchQuery
      }));
    }
  };

  const selectSearchResult = (result: SearchResult) => {
    const lat = parseFloat(result.lat);
    const lng = parseFloat(result.lon);
    
    if (webViewRef) {
      webViewRef.postMessage(JSON.stringify({
        type: 'goToLocation',
        lat: lat,
        lng: lng
      }));
    }
    
    setSearchResults([]);
    setSearchQuery('');
  };

  const handleCurrentLocationRequest = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission denied', 'Location permission is required to get your current location.');
        return;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });

      if (webViewRef) {
        webViewRef.postMessage(JSON.stringify({
          type: 'goToLocation',
          lat: location.coords.latitude,
          lng: location.coords.longitude
        }));
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to get current location. Please try again.');
    }
  };

  const getCurrentLocation = async () => {
    handleCurrentLocationRequest();
  };

  const styles = createStyles(theme);

  return (
    <View style={[styles.container, style]}>
      <View style={styles.header}>
        <Text style={styles.title}>📍 Select Location</Text>
        <Text style={styles.subtitle}>Tap on the map or search for a location</Text>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Ionicons name="search-outline" size={20} color={theme.colors.textSecondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search for a location..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            onSubmitEditing={handleSearch}
          />
          {loading && <ActivityIndicator size="small" color={theme.colors.primaryAccent} />}
        </View>
        
        <TouchableOpacity style={styles.searchButton} onPress={handleSearch}>
          <Ionicons name="search" size={20} color="white" />
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.locationButton} onPress={getCurrentLocation}>
          <Ionicons name="locate" size={20} color="white" />
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.fallbackButton} 
          onPress={() => {
            const fallbackLocation = {
              latitude: 20.2961,
              longitude: 85.8245,
              address: 'Bhubaneswar, Odisha, India',
              city: 'Bhubaneswar',
              state: 'Odisha',
              country: 'India'
            };
            setSelectedLocation(fallbackLocation);
            onLocationSelect(fallbackLocation);
            if (webViewRef) {
              webViewRef.postMessage(JSON.stringify({
                type: 'goToLocation',
                lat: fallbackLocation.latitude,
                lng: fallbackLocation.longitude
              }));
            }
          }}
        >
          <Ionicons name="home" size={20} color="white" />
        </TouchableOpacity>
      </View>

      {/* Search Results */}
      {searchResults.length > 0 && (
        <ScrollView style={styles.resultsContainer} nestedScrollEnabled>
          {searchResults.map((result, index) => (
            <TouchableOpacity
              key={index}
              style={styles.resultItem}
              onPress={() => selectSearchResult(result)}
            >
              <Ionicons name="location-outline" size={16} color={theme.colors.primaryAccent} />
              <Text style={styles.resultText} numberOfLines={2}>
                {result.display_name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      )}

      {/* Map */}
      <View style={styles.mapContainer}>
        {webViewError ? (
          <View style={styles.errorContainer}>
            <Ionicons name="alert-circle" size={48} color={theme.colors.error} />
            <Text style={styles.errorTitle}>Map Loading Error</Text>
            <Text style={styles.errorText}>{webViewError}</Text>
            <TouchableOpacity 
              style={styles.retryButton} 
              onPress={() => {
                setWebViewError(null);
                setMapLoaded(false);
              }}
            >
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <WebView
            ref={setWebViewRef}
            source={{ html: generateMapHTML(
              initialLocation?.latitude || currentLocation?.latitude || 20.2961, 
              initialLocation?.longitude || currentLocation?.longitude || 85.8245
            ) }}
            style={styles.map}
            onMessage={handleWebViewMessage}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          startInLoadingState={true}
          allowsInlineMediaPlayback={false}
          mediaPlaybackRequiresUserAction={true}
          mixedContentMode="compatibility"
          onShouldStartLoadWithRequest={(request) => {
            // Only allow the initial HTML load, block all external navigation
            return request.url === 'about:blank' || request.url.startsWith('data:');
          }}
          cacheEnabled={false}
          incognito={true}
          allowsBackForwardNavigationGestures={false}
          bounces={false}
          scrollEnabled={true}
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          overScrollMode="never"
          nestedScrollEnabled={false}
          onError={(syntheticEvent) => {
            const { nativeEvent } = syntheticEvent;
            console.warn('WebView error: ', nativeEvent);
            setWebViewError(`WebView error: ${nativeEvent.description || 'Unknown error'}`);
          }}
          onHttpError={(syntheticEvent) => {
            const { nativeEvent } = syntheticEvent;
            console.warn('WebView HTTP error: ', nativeEvent);
            setWebViewError(`HTTP error: ${nativeEvent.statusCode} - ${nativeEvent.description || 'Network error'}`);
          }}
          onNavigationStateChange={(navState) => {
            // Prevent any navigation that might cause the WebView to close
            if (navState.url !== 'about:blank' && !navState.url.startsWith('data:')) {
              console.warn('Blocked navigation to:', navState.url);
              return false;
            }
          }}
          renderLoading={() => (
            <View style={styles.mapLoading}>
              <ActivityIndicator size="large" color={theme.colors.primaryAccent} />
              <Text style={styles.mapLoadingText}>Loading map...</Text>
            </View>
          )}
          onLoadEnd={() => setMapLoaded(true)}
          />
        )}
      </View>

      {/* Selected Location Info */}
      {selectedLocation && selectedLocation.latitude != null && selectedLocation.longitude != null && (
        <View style={styles.selectedLocationContainer}>
          <Text style={styles.selectedLocationTitle}>Selected Location:</Text>
          <Text style={styles.coordinates}>
            Lat: {selectedLocation.latitude.toFixed(6)}, Lng: {selectedLocation.longitude.toFixed(6)}
          </Text>
        </View>
      )}
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    padding: 16,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.borderLight,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.textPrimary,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  searchContainer: {
    flexDirection: 'row',
    padding: 16,
    gap: 8,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: theme.colors.borderLight,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    color: theme.colors.textPrimary,
  },
  searchButton: {
    backgroundColor: theme.colors.primaryAccent,
    borderRadius: 12,
    padding: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  locationButton: {
    backgroundColor: theme.colors.secondaryAccent,
    borderRadius: 12,
    padding: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fallbackButton: {
    backgroundColor: theme.colors.success,
    borderRadius: 12,
    padding: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  resultsContainer: {
    maxHeight: 200,
    backgroundColor: theme.colors.surface,
    marginHorizontal: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.borderLight,
  },
  resultItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.borderLight,
  },
  resultText: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
    color: theme.colors.textPrimary,
  },
  mapContainer: {
    flex: 1,
    margin: 16,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: theme.colors.surface,
  },
  map: {
    flex: 1,
  },
  mapLoading: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
  },
  mapLoadingText: {
    marginTop: 8,
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  selectedLocationContainer: {
    padding: 16,
    backgroundColor: theme.colors.surface,
    borderTopWidth: 1,
    borderTopColor: theme.colors.borderLight,
  },
  selectedLocationTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.textPrimary,
    marginBottom: 4,
  },
  coordinates: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    fontFamily: 'monospace',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    backgroundColor: theme.colors.surface,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.error,
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  errorText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20,
  },
  retryButton: {
    backgroundColor: theme.colors.primaryAccent,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: theme.colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
});