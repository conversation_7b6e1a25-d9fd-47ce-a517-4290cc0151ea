import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { 
  TextInput, 
  Button, 
  Card, 
  IconButton,
  Portal,
  Modal,
  List,
} from 'react-native-paper';
import { useFormContext, Controller } from 'react-hook-form';
import { useAudioPlayer, useAudioRecorder, RecordingPresets, AudioModule } from 'expo-audio';
import { lightTheme } from '../../../config/theme';
import { BadgeFormData } from '../../../services/BadgeService';
import { Ionicons } from '@expo/vector-icons';

export default function BadgeClueStoryTab() {
  const { control, watch, setValue, formState: { errors } } = useFormContext<BadgeFormData>();
  const [audioUri, setAudioUri] = useState<string | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [audioModalVisible, setAudioModalVisible] = useState(false);
  
  const audioRecorder = useAudioRecorder(RecordingPresets.HIGH_QUALITY);
  const player = useAudioPlayer(audioUri || '');
  const [isPlaying, setIsPlaying] = useState(false);

  const clue = watch('clue');
  const story = watch('story');

  const startRecording = async () => {
    try {
      const { granted } = await AudioModule.requestRecordingPermissionsAsync();
      if (!granted) {
        Alert.alert('Permission required', 'Please grant microphone permission to record audio');
        return;
      }

      await audioRecorder.prepareToRecordAsync();
      audioRecorder.record();
      setIsRecording(true);
    } catch (error) {
      console.error('Failed to start recording:', error);
      Alert.alert('Error', 'Failed to start recording');
    }
  };

  const stopRecording = async () => {
    if (!isRecording) return;

    try {
      setIsRecording(false);
      await audioRecorder.stop();
      const uri = audioRecorder.uri;
      setAudioUri(uri);
      
      if (uri) {
        // Here you would typically upload the audio file to Firebase Storage
        // For now, we'll just store the local URI
        console.log('Audio recorded:', uri);
      }
    } catch (error) {
      console.error('Failed to stop recording:', error);
      Alert.alert('Error', 'Failed to stop recording');
    }
  };

  const playAudio = async () => {
    if (!audioUri) return;

    try {
      if (isPlaying) {
        player.pause();
        setIsPlaying(false);
      } else {
        player.play();
        setIsPlaying(true);
      }
    } catch (error) {
      console.error('Failed to play audio:', error);
      Alert.alert('Error', 'Failed to play audio');
    }
  };

  const stopAudio = async () => {
    if (player) {
      player.pause();
      setIsPlaying(false);
    }
  };

  const deleteAudio = () => {
    Alert.alert(
      'Delete Audio',
      'Are you sure you want to delete this audio recording?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setAudioUri(null);
            if (player) {
              player.pause();
            }
            setIsPlaying(false);
          },
        },
      ]
    );
  };

  const getWordCount = (text: string) => {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  };

  const getCharacterCount = (text: string) => {
    return text.length;
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.sectionTitle}>🔍 Clue</Text>
          <Text style={styles.sectionSubtitle}>
            Write a riddle or clue that will guide users to the badge location
          </Text>
          
          <View style={styles.inputContainer}>
            <Controller
              name="clue"
              control={control}
              render={({ field: { onChange, value } }) => (
                <TextInput
                  mode="outlined"
                  value={value}
                  onChangeText={onChange}
                  placeholder="Enter your clue here..."
                  multiline
                  numberOfLines={4}
                  style={styles.textArea}
                  error={!!errors.clue}
                />
              )}
            />
            {errors.clue && (
              <Text style={styles.errorText}>{errors.clue.message}</Text>
            )}
            
            <View style={styles.textStats}>
              <Text style={styles.statText}>
                {getWordCount(clue || '')} words • {getCharacterCount(clue || '')} characters
              </Text>
            </View>
          </View>

          <View style={styles.tipContainer}>
            <Text style={styles.tipTitle}>💡 Clue Tips:</Text>
            <Text style={styles.tipText}>• Make it challenging but not impossible</Text>
            <Text style={styles.tipText}>• Include landmarks or distinctive features</Text>
            <Text style={styles.tipText}>• Consider the difficulty level for your audience</Text>
          </View>
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.sectionTitle}>📖 Story</Text>
          <Text style={styles.sectionSubtitle}>
            Tell the story behind this location or badge
          </Text>
          
          <View style={styles.inputContainer}>
            <Controller
              name="story"
              control={control}
              render={({ field: { onChange, value } }) => (
                <TextInput
                  mode="outlined"
                  value={value}
                  onChangeText={onChange}
                  placeholder="Share the story, history, or significance of this location..."
                  multiline
                  numberOfLines={6}
                  style={styles.textArea}
                  error={!!errors.story}
                />
              )}
            />
            {errors.story && (
              <Text style={styles.errorText}>{errors.story.message}</Text>
            )}
            
            <View style={styles.textStats}>
              <Text style={styles.statText}>
                {getWordCount(story || '')} words • {getCharacterCount(story || '')} characters
              </Text>
            </View>
          </View>

          {/* Audio Story Option */}
          <View style={styles.audioSection}>
            <Text style={styles.audioTitle}>🎙️ Audio Story (Optional)</Text>
            <Text style={styles.audioSubtitle}>
              Record an audio narration to accompany your written story
            </Text>
            
            <View style={styles.audioControls}>
              {!audioUri ? (
                <Button
                  mode="contained"
                  onPress={isRecording ? stopRecording : startRecording}
                  icon={isRecording ? 'stop' : 'microphone'}
                  loading={isRecording}
                  style={[
                    styles.audioButton,
                    isRecording && styles.recordingButton
                  ]}
                >
                  {isRecording ? 'Stop Recording' : 'Start Recording'}
                </Button>
              ) : (
                <View style={styles.audioPlayback}>
                  <Button
                    mode="contained"
                    onPress={isPlaying ? stopAudio : playAudio}
                    icon={isPlaying ? 'stop' : 'play'}
                    style={styles.audioButton}
                  >
                    {isPlaying ? 'Stop' : 'Play'}
                  </Button>
                  <IconButton
                    icon="delete"
                    size={24}
                    onPress={deleteAudio}
                    iconColor={lightTheme.colors.error}
                  />
                </View>
              )}
            </View>
          </View>
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.sectionTitle}>✨ Preview</Text>
          
          <View style={styles.previewContainer}>
            <Text style={styles.previewLabel}>Clue:</Text>
            <Text style={styles.previewText}>
              {clue || 'Your clue will appear here...'}
            </Text>
            
            <Text style={styles.previewLabel}>Story:</Text>
            <Text style={styles.previewText}>
              {story || 'Your story will appear here...'}
            </Text>
            
            {audioUri && (
              <View style={styles.audioPreview}>
                <Text style={styles.previewLabel}>Audio Story:</Text>
                <Button
                  mode="outlined"
                  onPress={playAudio}
                  icon="play"
                  compact
                >
                  Play Audio
                </Button>
              </View>
            )}
          </View>
        </Card.Content>
      </Card>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  card: {
    marginBottom: lightTheme.spacing.lg,
    borderRadius: lightTheme.borderRadius.lg,
  },
  sectionTitle: {
    ...lightTheme.typography.h6,
    color: lightTheme.colors.textPrimary,
    marginBottom: lightTheme.spacing.sm,
  },
  sectionSubtitle: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textSecondary,
    marginBottom: lightTheme.spacing.lg,
  },
  inputContainer: {
    marginBottom: lightTheme.spacing.lg,
  },
  textArea: {
    backgroundColor: lightTheme.colors.surface,
    minHeight: 100,
  },
  textStats: {
    alignItems: 'flex-end',
    marginTop: lightTheme.spacing.sm,
  },
  statText: {
    ...lightTheme.typography.caption,
    color: lightTheme.colors.textSecondary,
  },
  tipContainer: {
    backgroundColor: lightTheme.colors.info + '10',
    padding: lightTheme.spacing.md,
    borderRadius: lightTheme.borderRadius.md,
    borderLeftWidth: 4,
    borderLeftColor: lightTheme.colors.info,
  },
  tipTitle: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.info,
    fontWeight: '600',
    marginBottom: lightTheme.spacing.sm,
  },
  tipText: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.info,
    marginBottom: lightTheme.spacing.xs,
  },
  audioSection: {
    marginTop: lightTheme.spacing.lg,
    padding: lightTheme.spacing.md,
    backgroundColor: lightTheme.colors.surface,
    borderRadius: lightTheme.borderRadius.md,
    borderWidth: 1,
    borderColor: lightTheme.colors.borderLight,
  },
  audioTitle: {
    ...lightTheme.typography.h6,
    color: lightTheme.colors.textPrimary,
    marginBottom: lightTheme.spacing.sm,
  },
  audioSubtitle: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textSecondary,
    marginBottom: lightTheme.spacing.lg,
  },
  audioControls: {
    alignItems: 'center',
  },
  audioButton: {
    minWidth: 150,
  },
  recordingButton: {
    backgroundColor: lightTheme.colors.error,
  },
  audioPlayback: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: lightTheme.spacing.md,
  },
  previewContainer: {
    backgroundColor: lightTheme.colors.background,
    padding: lightTheme.spacing.md,
    borderRadius: lightTheme.borderRadius.md,
    borderWidth: 1,
    borderColor: lightTheme.colors.borderLight,
  },
  previewLabel: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textPrimary,
    fontWeight: '600',
    marginBottom: lightTheme.spacing.sm,
    marginTop: lightTheme.spacing.md,
  },
  previewText: {
    ...lightTheme.typography.body1,
    color: lightTheme.colors.textSecondary,
    fontStyle: 'italic',
    marginBottom: lightTheme.spacing.md,
  },
  audioPreview: {
    marginTop: lightTheme.spacing.md,
  },
  errorText: {
    ...lightTheme.typography.caption,
    color: lightTheme.colors.error,
    marginTop: lightTheme.spacing.sm,
  },
});