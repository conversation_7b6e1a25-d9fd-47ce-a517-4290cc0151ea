import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { 
  createUserWithEmailAndPassword, 
  signInWithEmailAndPassword, 
  signOut, 
  sendEmailVerification,
  User,
  updateProfile,
  reload
} from 'firebase/auth';
import { doc, setDoc, getDoc, updateDoc } from 'firebase/firestore';
import { auth, db } from '../config/firebase';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface UserProfile {
  uid: string;
  email: string;
  username: string;
  fullName: string;
  mobile?: string;
  avatar?: string;
  level: number;
  xp: number;
  badges: string[];
  questsCompleted: number;
  createdAt: string;
  emailVerified: boolean;
  role: 'user' | 'admin';
  active: boolean;
}

interface AuthState {
  user: UserProfile | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  registrationStep: 'email' | 'details' | 'verification' | 'complete';
  tempRegistrationData: {
    email?: string;
    uid?: string;
  };
}

const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  registrationStep: 'email',
  tempRegistrationData: {},
};

// Check if email exists
export const checkEmailExists = createAsyncThunk(
  'auth/checkEmailExists',
  async (email: string, { rejectWithValue }) => {
    try {
      // Try to create a temporary user to check if email exists
      const tempPassword = 'TempCheckPassword123!';
      await createUserWithEmailAndPassword(auth, email, tempPassword);
      // If we get here, email doesn't exist, so delete the temp user
      if (auth.currentUser) {
        await auth.currentUser.delete();
      }
      return false; // Email doesn't exist
    } catch (error: any) {
      if (error.code === 'auth/email-already-in-use') {
        return true; // Email exists
      }
      return rejectWithValue(error.message);
    }
  }
);

// Check if email exists and prepare for registration
export const checkEmailForRegistration = createAsyncThunk(
  'auth/checkEmailForRegistration',
  async (email: string, { rejectWithValue }) => {
    try {
      // Try to create a temporary user to check if email exists
      const tempPassword = 'TempCheckPassword123!';
      await createUserWithEmailAndPassword(auth, email, tempPassword);
      // If we get here, email doesn't exist, so delete the temp user
      if (auth.currentUser) {
        await auth.currentUser.delete();
      }
      return { email, available: true };
    } catch (error: any) {
      if (error.code === 'auth/email-already-in-use') {
        return rejectWithValue('This email is already registered. Please try logging in instead.');
      }
      return rejectWithValue('Failed to check email availability. Please try again.');
    }
  }
);

// Complete registration with user details
export const completeRegistration = createAsyncThunk(
  'auth/completeRegistration',
  async (userData: {
    email: string;
    username: string;
    fullName: string;
    mobile?: string;
    password: string;
  }, { rejectWithValue }) => {
    try {
      // Check if username is already taken
      const usernameDoc = await getDoc(doc(db, 'usernames', userData.username.toLowerCase()));
      if (usernameDoc.exists()) {
        return rejectWithValue('Username is already taken. Please choose another one.');
      }

      // Create user with actual credentials
      const userCredential = await createUserWithEmailAndPassword(auth, userData.email, userData.password);
      
      // Update profile
      await updateProfile(userCredential.user, {
        displayName: userData.fullName,
      });

      // Create user profile in Firestore
      const userProfile: UserProfile = {
        uid: userCredential.user.uid,
        email: userData.email,
        username: userData.username,
        fullName: userData.fullName,
        mobile: userData.mobile,
        level: 1,
        xp: 0,
        badges: [],
        questsCompleted: 0,
        createdAt: new Date().toISOString(),
        emailVerified: false,
        role: 'user',
        active: true,
      };

      // Save user profile
      await setDoc(doc(db, 'users', userCredential.user.uid), userProfile);
      
      // Reserve username
      await setDoc(doc(db, 'usernames', userData.username.toLowerCase()), {
        uid: userCredential.user.uid,
        username: userData.username,
      });

      // Send email verification
      await sendEmailVerification(userCredential.user);

      return userProfile;
    } catch (error: any) {
      if (error.code === 'auth/email-already-in-use') {
        return rejectWithValue('This email is already registered. Please try logging in instead.');
      }
      return rejectWithValue(error.message || 'Registration failed. Please try again.');
    }
  }
);

// Login with email and password
export const loginWithPassword = createAsyncThunk(
  'auth/loginWithPassword',
  async ({ email, password }: { email: string; password: string }, { rejectWithValue }) => {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      
      // Check if email is verified
      if (!userCredential.user.emailVerified) {
        await signOut(auth);
        return rejectWithValue('Please verify your email before logging in. Check your inbox for the verification link.');
      }

      // Get user profile from Firestore
      const userDoc = await getDoc(doc(db, 'users', userCredential.user.uid));
      if (userDoc.exists()) {
        const userProfile = { ...userDoc.data(), emailVerified: userCredential.user.emailVerified } as UserProfile;
        
        // Store auth state in AsyncStorage
        await AsyncStorage.setItem('isAuthenticated', 'true');
        await AsyncStorage.setItem('userProfile', JSON.stringify(userProfile));
        
        return userProfile;
      } else {
        await signOut(auth);
        return rejectWithValue('User profile not found. Please contact support.');
      }
    } catch (error: any) {
      if (error.code === 'auth/user-not-found') {
        return rejectWithValue('No account found with this email. Please register first.');
      } else if (error.code === 'auth/wrong-password') {
        return rejectWithValue('Incorrect password. Please try again.');
      } else if (error.code === 'auth/invalid-email') {
        return rejectWithValue('Invalid email address.');
      } else if (error.code === 'auth/user-disabled') {
        return rejectWithValue('This account has been disabled. Please contact support.');
      }
      return rejectWithValue('Login failed. Please try again.');
    }
  }
);

// Verify email
export const verifyEmail = createAsyncThunk(
  'auth/verifyEmail',
  async (_, { rejectWithValue }) => {
    try {
      if (auth.currentUser) {
        await reload(auth.currentUser);
        if (auth.currentUser.emailVerified) {
          // Update user profile in Firestore
          await updateDoc(doc(db, 'users', auth.currentUser.uid), {
            emailVerified: true,
          });
          
          // Get updated user profile
          const userDoc = await getDoc(doc(db, 'users', auth.currentUser.uid));
          if (userDoc.exists()) {
            const userProfile = { ...userDoc.data(), emailVerified: true } as UserProfile;
            
            // Store auth state in AsyncStorage
            await AsyncStorage.setItem('isAuthenticated', 'true');
            await AsyncStorage.setItem('userProfile', JSON.stringify(userProfile));
            
            return userProfile;
          }
        } else {
          return rejectWithValue('Email not verified yet. Please check your inbox and click the verification link.');
        }
      }
      return rejectWithValue('No user found.');
    } catch (error: any) {
      return rejectWithValue('Email verification failed. Please try again.');
    }
  }
);

// Logout
export const logout = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      await signOut(auth);
      await AsyncStorage.removeItem('isAuthenticated');
      await AsyncStorage.removeItem('userProfile');
      return null;
    } catch (error: any) {
      return rejectWithValue('Logout failed. Please try again.');
    }
  }
);

// Load user from storage
export const loadUserFromStorage = createAsyncThunk(
  'auth/loadUserFromStorage',
  async (_, { rejectWithValue }) => {
    try {
      const isAuthenticated = await AsyncStorage.getItem('isAuthenticated');
      const userProfileString = await AsyncStorage.getItem('userProfile');
      
      if (isAuthenticated === 'true' && userProfileString) {
        const userProfile = JSON.parse(userProfileString) as UserProfile;
        return userProfile;
      }
      
      return null;
    } catch (error: any) {
      return rejectWithValue('Failed to load user data.');
    }
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setRegistrationStep: (state, action: PayloadAction<AuthState['registrationStep']>) => {
      state.registrationStep = action.payload;
    },
    clearTempRegistrationData: (state) => {
      state.tempRegistrationData = {};
      state.registrationStep = 'email';
    },
  },
  extraReducers: (builder) => {
    builder
      // Check email exists
      .addCase(checkEmailExists.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(checkEmailExists.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(checkEmailExists.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Check email for registration
      .addCase(checkEmailForRegistration.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(checkEmailForRegistration.fulfilled, (state, action) => {
        state.isLoading = false;
        state.tempRegistrationData = { email: action.payload.email };
        state.registrationStep = 'details';
      })
      .addCase(checkEmailForRegistration.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Complete registration
      .addCase(completeRegistration.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(completeRegistration.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
        state.registrationStep = 'verification';
      })
      .addCase(completeRegistration.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Login
      .addCase(loginWithPassword.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginWithPassword.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
        state.isAuthenticated = true;
      })
      .addCase(loginWithPassword.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Verify email
      .addCase(verifyEmail.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(verifyEmail.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
        state.isAuthenticated = true;
        state.registrationStep = 'complete';
      })
      .addCase(verifyEmail.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Logout
      .addCase(logout.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(logout.fulfilled, (state) => {
        state.isLoading = false;
        state.user = null;
        state.isAuthenticated = false;
        state.tempRegistrationData = {};
        state.registrationStep = 'email';
        state.error = null;
      })
      .addCase(logout.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Load user from storage
      .addCase(loadUserFromStorage.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(loadUserFromStorage.fulfilled, (state, action) => {
        state.isLoading = false;
        if (action.payload) {
          state.user = action.payload;
          state.isAuthenticated = true;
        }
      })
      .addCase(loadUserFromStorage.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, setRegistrationStep, clearTempRegistrationData } = authSlice.actions;
export default authSlice.reducer;