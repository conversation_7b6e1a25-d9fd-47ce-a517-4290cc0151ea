import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  ScrollView,
} from 'react-native';
import { Searchbar, Card, Chip, Button } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import { lightTheme } from '../../config/theme';
import { useBadgeFilter, Badge } from '../../hooks/useBadgeFilter';
import { collection, getDocs, doc, getDoc } from 'firebase/firestore';
import { db } from '../../config/firebase';
import { useAuth } from '../../hooks/useAuth';

export default function BadgeListView() {
  const [badges, setBadges] = useState<Badge[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();
  const navigation = useNavigation();
  
  const {
    filteredBadges,
    filterOptions,
    categories,
    updateSearchQuery,
    updateCategory,
    toggleUnlockedOnly,
    toggleLockedOnly,
    clearFilters,
    getFilterStats,
  } = useBadgeFilter(badges);

  useEffect(() => {
    loadBadges();
  }, []);

  const loadBadges = async () => {
    try {
      setLoading(true);
      
      // Load all badges
      const badgesSnapshot = await getDocs(collection(db, 'badges'));
      const badgesList: Badge[] = [];

      for (const badgeDoc of badgesSnapshot.docs) {
        const badgeData = { id: badgeDoc.id, ...badgeDoc.data() } as Badge;
        
        // Check if user has unlocked this badge
        if (user) {
          const unlockedDoc = await getDoc(
            doc(db, `users/${user.uid}/unlocked/${badgeDoc.id}`)
          );
          
          if (unlockedDoc.exists()) {
            const unlockedData = unlockedDoc.data();
            badgeData.isUnlocked = true;
            badgeData.unlockedAt = unlockedData.unlockedAt;
            badgeData.viaHint = unlockedData.viaHint || false;
          } else {
            badgeData.isUnlocked = false;
          }
        }
        
        badgesList.push(badgeData);
      }
      
      setBadges(badgesList);
    } catch (error) {
      console.error('Error loading badges:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBadgePress = (badge: Badge) => {
    // Navigate to badge detail
    navigation.navigate('BadgeDetail' as never, { badgeId: badge.id } as never);
  };

  const renderBadgeCard = ({ item: badge }: { item: Badge }) => (
    <TouchableOpacity
      style={styles.cardContainer}
      onPress={() => handleBadgePress(badge)}
      activeOpacity={0.8}
    >
      <Card style={[
        styles.badgeCard,
        badge.isUnlocked && styles.badgeCardUnlocked
      ]}>
        <View style={styles.cardContent}>
          {/* Badge Icon and Status */}
          <View style={styles.badgeIconContainer}>
            <LinearGradient
              colors={badge.isUnlocked ? 
                [lightTheme.colors.accent5, lightTheme.colors.success] :
                [lightTheme.colors.textSecondary, lightTheme.colors.borderLight]
              }
              style={styles.badgeIcon}
            >
              <Text style={styles.badgeIconText}>{badge.icon}</Text>
            </LinearGradient>
            
            {badge.isUnlocked ? (
              <View style={styles.statusUnlocked}>
                <Ionicons name="checkmark-circle-outline" size={20} color={lightTheme.colors.success} />
              </View>
            ) : (
              <View style={styles.statusLocked}>
                <Ionicons name="lock-closed" size={16} color={lightTheme.colors.textSecondary} />
              </View>
            )}
          </View>

          {/* Badge Info */}
          <View style={styles.badgeInfo}>
            <Text style={styles.badgeTitle} numberOfLines={1}>
              {badge.title}
            </Text>
            
            <View style={styles.badgeMetaRow}>
              <View style={styles.categoryChip}>
                <Text style={styles.categoryText}>{badge.category}</Text>
              </View>
              
              <View style={[
                styles.difficultyChip,
                { backgroundColor: getDifficultyColor(badge.difficulty) + '20' }
              ]}>
                <Text style={[
                  styles.difficultyText,
                  { color: getDifficultyColor(badge.difficulty) }
                ]}>
                  {badge.difficulty.toUpperCase()}
                </Text>
              </View>
            </View>
            
            <Text style={styles.badgeDescription} numberOfLines={2}>
              {badge.description}
            </Text>

            {badge.isUnlocked && badge.viaHint && (
              <View style={styles.hintBadge}>
                <Ionicons name="bulb" size={12} color={lightTheme.colors.warning} />
                <Text style={styles.hintText}>Used Hint</Text>
              </View>
            )}
          </View>

          {/* Arrow */}
          <View style={styles.arrowContainer}>
            <Ionicons 
              name="chevron-forward" 
              size={20} 
              color={lightTheme.colors.textSecondary} 
            />
          </View>
        </View>
      </Card>
    </TouchableOpacity>
  );

  const renderFilterChips = () => (
    <View style={styles.filterContainer}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.filterScrollContent}
      >
        {categories.map((category) => (
          <Chip
            key={category}
            selected={filterOptions.selectedCategory === category || 
                     (category === 'All' && !filterOptions.selectedCategory)}
            onPress={() => updateCategory(category === 'All' ? null : category)}
            style={[
              styles.filterChip,
              (filterOptions.selectedCategory === category || 
               (category === 'All' && !filterOptions.selectedCategory)) && 
              styles.filterChipSelected
            ]}
            textStyle={styles.filterChipText}
          >
            {category}
          </Chip>
        ))}
        
        <Chip
          selected={filterOptions.showUnlockedOnly}
          onPress={toggleUnlockedOnly}
          style={[
            styles.filterChip,
            filterOptions.showUnlockedOnly && styles.filterChipSelected
          ]}
          textStyle={styles.filterChipText}
          icon="check-circle"
        >
          Unlocked
        </Chip>
        
        <Chip
          selected={filterOptions.showLockedOnly}
          onPress={toggleLockedOnly}
          style={[
            styles.filterChip,
            filterOptions.showLockedOnly && styles.filterChipSelected
          ]}
          textStyle={styles.filterChipText}
          icon="lock"
        >
          Locked
        </Chip>
      </ScrollView>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyStateIcon}>🔍</Text>
      <Text style={styles.emptyStateTitle}>No badges found</Text>
      <Text style={styles.emptyStateText}>
        Try adjusting your filters or search terms
      </Text>
      <Button
        mode="outlined"
        onPress={clearFilters}
        style={styles.clearFiltersButton}
      >
        Clear Filters
      </Button>
    </View>
  );

  const stats = getFilterStats();

  return (
    <View style={styles.container}>
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Search badges..."
          value={filterOptions.searchQuery}
          onChangeText={updateSearchQuery}
          style={styles.searchBar}
          inputStyle={styles.searchInput}
          iconColor={lightTheme.colors.textSecondary}
        />
      </View>

      {/* Filter Chips */}
      {renderFilterChips()}

      {/* Stats */}
      <View style={styles.statsContainer}>
        <Text style={styles.statsText}>
          {stats.filtered} of {stats.total} badges • {stats.unlocked} unlocked
        </Text>
        {(filterOptions.searchQuery || filterOptions.selectedCategory || 
          filterOptions.showUnlockedOnly || filterOptions.showLockedOnly) && (
          <TouchableOpacity onPress={clearFilters}>
            <Text style={styles.clearFiltersText}>Clear all</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Badge List */}
      <FlatList
        data={filteredBadges}
        renderItem={renderBadgeCard}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        refreshing={loading}
        onRefresh={loadBadges}
        ListEmptyComponent={!loading ? renderEmptyState : null}
      />
    </View>
  );
}

const getDifficultyColor = (difficulty: string) => {
  switch (difficulty) {
    case 'easy': return lightTheme.colors.success;
    case 'medium': return lightTheme.colors.warning;
    case 'hard': return lightTheme.colors.error;
    case 'expert': return lightTheme.colors.accent1;
    default: return lightTheme.colors.textSecondary;
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: lightTheme.colors.background,
  },
  searchContainer: {
    paddingHorizontal: lightTheme.spacing.lg,
    paddingVertical: lightTheme.spacing.md,
  },
  searchBar: {
    backgroundColor: lightTheme.colors.surface,
    borderRadius: lightTheme.borderRadius.lg,
    elevation: 2,
  },
  searchInput: {
    ...lightTheme.typography.body1,
  },
  filterContainer: {
    paddingBottom: lightTheme.spacing.md,
  },
  filterScrollContent: {
    paddingHorizontal: lightTheme.spacing.lg,
    gap: lightTheme.spacing.sm,
  },
  filterChip: {
    backgroundColor: lightTheme.colors.surface,
    borderColor: lightTheme.colors.borderLight,
  },
  filterChipSelected: {
    backgroundColor: lightTheme.colors.primaryAccent,
  },
  filterChipText: {
    ...lightTheme.typography.caption,
    fontWeight: '600',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: lightTheme.spacing.lg,
    paddingBottom: lightTheme.spacing.md,
  },
  statsText: {
    ...lightTheme.typography.caption,
    color: lightTheme.colors.textSecondary,
  },
  clearFiltersText: {
    ...lightTheme.typography.caption,
    color: lightTheme.colors.primaryAccent,
    fontWeight: '600',
  },
  listContent: {
    paddingHorizontal: lightTheme.spacing.lg,
    paddingBottom: lightTheme.spacing.xl,
  },
  cardContainer: {
    marginBottom: lightTheme.spacing.md,
  },
  badgeCard: {
    backgroundColor: lightTheme.colors.surface,
    borderRadius: lightTheme.borderRadius.lg,
    elevation: 2,
    ...lightTheme.shadows.small,
  },
  badgeCardUnlocked: {
    borderLeftWidth: 4,
    borderLeftColor: lightTheme.colors.success,
  },
  cardContent: {
    flexDirection: 'row',
    padding: lightTheme.spacing.lg,
    alignItems: 'center',
  },
  badgeIconContainer: {
    position: 'relative',
    marginRight: lightTheme.spacing.lg,
  },
  badgeIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    ...lightTheme.shadows.small,
  },
  badgeIconText: {
    fontSize: 24,
  },
  statusUnlocked: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    backgroundColor: lightTheme.colors.surface,
    borderRadius: 12,
    padding: 2,
  },
  statusLocked: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    backgroundColor: lightTheme.colors.surface,
    borderRadius: 10,
    padding: 3,
  },
  badgeInfo: {
    flex: 1,
  },
  badgeTitle: {
    ...lightTheme.typography.h6,
    color: lightTheme.colors.textPrimary,
    marginBottom: lightTheme.spacing.xs,
    fontWeight: '600',
  },
  badgeMetaRow: {
    flexDirection: 'row',
    marginBottom: lightTheme.spacing.sm,
    gap: lightTheme.spacing.sm,
  },
  categoryChip: {
    backgroundColor: lightTheme.colors.primaryAccent + '20',
    paddingHorizontal: lightTheme.spacing.sm,
    paddingVertical: lightTheme.spacing.xs,
    borderRadius: lightTheme.borderRadius.sm,
  },
  categoryText: {
    ...lightTheme.typography.caption,
    color: lightTheme.colors.primaryAccent,
    fontWeight: '600',
  },
  difficultyChip: {
    paddingHorizontal: lightTheme.spacing.sm,
    paddingVertical: lightTheme.spacing.xs,
    borderRadius: lightTheme.borderRadius.sm,
  },
  difficultyText: {
    ...lightTheme.typography.caption,
    fontWeight: '600',
    fontSize: 10,
  },
  badgeDescription: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textSecondary,
    lineHeight: 18,
  },
  hintBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: lightTheme.spacing.xs,
    gap: lightTheme.spacing.xs,
  },
  hintText: {
    ...lightTheme.typography.caption,
    color: lightTheme.colors.warning,
    fontWeight: '600',
  },
  arrowContainer: {
    marginLeft: lightTheme.spacing.sm,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: lightTheme.spacing.xxxl,
  },
  emptyStateIcon: {
    fontSize: 48,
    marginBottom: lightTheme.spacing.lg,
  },
  emptyStateTitle: {
    ...lightTheme.typography.h5,
    color: lightTheme.colors.textPrimary,
    marginBottom: lightTheme.spacing.sm,
    fontWeight: '600',
  },
  emptyStateText: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: lightTheme.spacing.lg,
  },
  clearFiltersButton: {
    borderColor: lightTheme.colors.primaryAccent,
  },
});