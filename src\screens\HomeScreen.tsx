import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Card } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../App';
import { lightTheme, levelBadges, createGlowEffect } from '../config/theme';
import { useTheme } from '../contexts/ThemeContext';
import ProgressDonut from '../components/ProgressDonut';
import QuestCard from '../components/QuestCard';
import { trackQuestTap, trackMapView, trackScreenView } from '../services/analyticsService';

const { width } = Dimensions.get('window');

type HomeScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Home'>;

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: theme.spacing.xl,
    paddingTop: theme.spacing.lg,
    borderBottomLeftRadius: theme.borderRadius.xxl,
    borderBottomRightRadius: theme.borderRadius.xxl,
    ...theme.shadows.large,
  },
  welcomeSection: {
    marginBottom: theme.spacing.lg,
  },
  userProfile: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: theme.spacing.lg,
  },
  avatarGradient: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    ...theme.shadows.medium,
  },
  avatarIcon: {
    fontSize: 36,
    color: theme.colors.white,
  },
  levelBadge: {
    position: 'absolute',
    bottom: -5,
    right: -5,
    backgroundColor: theme.colors.secondaryAccent,
    borderRadius: theme.borderRadius.round,
    width: 28,
    height: 28,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: theme.colors.white,
    ...theme.shadows.small,
  },
  levelText: {
    ...theme.typography.buttonSmall,
    color: theme.colors.white,
    fontWeight: '800',
  },
  userInfo: {
    flex: 1,
  },
  statsRow: {
    flexDirection: 'row',
    marginTop: theme.spacing.md,
    gap: theme.spacing.lg,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    ...theme.typography.h6,
    color: theme.colors.white,
    fontWeight: '800',
  },
  statLabel: {
    ...theme.typography.caption,
    color: theme.colors.white,
    opacity: 0.8,
    marginTop: 2,
  },
  greeting: {
    ...theme.typography.h3,
    color: theme.colors.white,
    marginBottom: theme.spacing.xs,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  subGreeting: {
    ...theme.typography.body1,
    color: theme.colors.white,
    opacity: 0.9,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  achievementTeaser: {
    padding: theme.spacing.lg,
    borderRadius: theme.borderRadius.xl,
    alignItems: 'center',
    ...theme.shadows.small,
  },
  achievementText: {
    ...theme.typography.body2,
    color: theme.colors.white,
    fontWeight: '700',
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  progressSection: {
    paddingHorizontal: theme.spacing.lg,
    marginBottom: theme.spacing.lg,
  },
  questSection: {
    marginBottom: theme.spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.lg,
    marginBottom: theme.spacing.md,
  },
  sectionTitle: {
    ...theme.typography.h4,
    color: theme.colors.textPrimary,
  },
  seeAllText: {
    ...theme.typography.body2,
    color: theme.colors.primaryAccent,
    fontWeight: '600',
  },
  questCarousel: {
    paddingHorizontal: theme.spacing.lg,
  },
  mapSection: {
    paddingHorizontal: theme.spacing.lg,
    marginBottom: theme.spacing.lg,
  },
  mapCard: {
    borderRadius: theme.borderRadius.xxl,
    ...theme.shadows.large,
  },
  mapHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.xl,
    paddingBottom: theme.spacing.md,
  },
  mapTitle: {
    ...theme.typography.h4,
    color: theme.colors.textPrimary,
  },
  viewMapButton: {
    borderRadius: theme.borderRadius.lg,
    overflow: 'hidden',
  },
  viewMapGradient: {
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
  },
  viewMapText: {
    ...theme.typography.buttonSmall,
    color: theme.colors.white,
    fontWeight: '700',
  },
  mapPreview: {
    height: 140,
    backgroundColor: theme.colors.background,
    margin: theme.spacing.xl,
    marginTop: 0,
    borderRadius: theme.borderRadius.xl,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    ...theme.shadows.small,
  },
  mapPlaceholder: {
    ...theme.typography.h6,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.xs,
  },
  mapSubtext: {
    ...theme.typography.body2,
    color: theme.colors.textSecondary,
  },
  mapPins: {
    flexDirection: 'row',
    position: 'absolute',
    bottom: theme.spacing.md,
    gap: theme.spacing.sm,
  },
  mapPin: {
    width: 16,
    height: 16,
    borderRadius: 8,
    ...theme.shadows.medium,
  },
  socialSection: {
    paddingHorizontal: theme.spacing.lg,
    marginBottom: theme.spacing.lg,
  },
  socialCard: {
    borderRadius: theme.borderRadius.xl,
    padding: theme.spacing.lg,
    marginTop: theme.spacing.md,
    ...theme.shadows.medium,
  },
  achievementItem: {
    paddingVertical: theme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.borderLight,
  },
  achievementGlow: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.lg,
    marginVertical: theme.spacing.xs,
  },
  achievementItemText: {
    ...theme.typography.body2,
    color: theme.colors.textPrimary,
    fontWeight: '600',
  },
  socialItem: {
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.borderLight,
  },
  socialItemText: {
    ...theme.typography.body2,
    color: theme.colors.textSecondary,
  },
  fomoContainer: {
    padding: theme.spacing.lg,
    borderRadius: theme.borderRadius.xl,
    alignItems: 'center',
    marginTop: theme.spacing.lg,
    ...theme.shadows.medium,
  },
  fomoText: {
    ...theme.typography.body2,
    color: theme.colors.white,
    fontWeight: '700',
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
});

export default function HomeScreen() {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const [userName, setUserName] = useState('Explorer');
  const [userStats, setUserStats] = useState({
    totalBadges: 5,
    maxBadges: 15,
    questsCompleted: 8,
    level: 3,
    streak: 7,
    totalXP: 2450,
    nextLevelXP: 3000,
  });
  
  const currentLevel = levelBadges[userStats.level] || levelBadges[1];
  const nextLevel = levelBadges[userStats.level + 1] || levelBadges[8];
  const xpProgress = (userStats.totalXP % 1000) / 1000; // Simple XP calculation
  
  const navigation = useNavigation<HomeScreenNavigationProp>();

  useEffect(() => {
    trackScreenView('Home');
  }, []);

  const getTimeGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  const mockQuests = [
    {
      id: '1',
      title: 'Temple Tracker',
      description: 'Discover ancient temples and their hidden stories across the city',
      coverImage: 'https://images.unsplash.com/photo-1544735716-392fe2489ffa?w=400&h=300&fit=crop',
      progress: 3,
      totalSteps: 7,
      difficulty: 'Medium' as const,
      category: 'Historical',
    },
    {
      id: '2',
      title: 'Street Art Safari',
      description: 'Find and photograph vibrant murals in the artistic quarter',
      coverImage: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=400&h=300&fit=crop',
      progress: 0,
      totalSteps: 5,
      difficulty: 'Easy' as const,
      category: 'Art & Culture',
    },
    {
      id: '3',
      title: 'Culinary Quest',
      description: 'Taste authentic local dishes from 10 hidden food gems',
      coverImage: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop',
      progress: 6,
      totalSteps: 10,
      difficulty: 'Hard' as const,
      category: 'Food',
    },
    {
      id: '4',
      title: 'Nature Walks',
      description: 'Explore parks, gardens, and green spaces within the city',
      coverImage: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400&h=300&fit=crop',
      progress: 2,
      totalSteps: 6,
      difficulty: 'Easy' as const,
      category: 'Nature',
    },
  ];

  const handleQuestPress = (questId: string, questTitle: string) => {
    // Track quest tap
    trackQuestTap(questId, questTitle, 'user123'); // Replace with actual user ID
    
    Alert.alert(
      'Quest Started!',
      `Ready to begin ${questTitle}? Others in your city are already exploring!`,
      [
        { text: 'Maybe Later', style: 'cancel' },
        { text: 'Start Quest', onPress: () => console.log('Quest started:', questId) },
      ]
    );
  };

  const handleMapPress = () => {
    trackMapView('user123'); // Replace with actual user ID
    Alert.alert('Map View', 'Full screen map with quest pins will open here!');
  };

  const recentAchievements = [
    '🏆 Completed "Historic Downtown" quest',
    '🎯 Reached Level 3 Explorer status',
    '⭐ Earned "Temple Master" badge',
  ];

  const socialFeed = [
    'Alex just completed "Street Art Safari" - 3 new badges! 🎨',
    'Sarah discovered a hidden gem in "Culinary Quest" 🍜',
    'Mike is 1 step away from "Temple Master" badge! 🏛️',
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <LinearGradient
          colors={theme.gradients.primary as any}
          style={styles.header}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.welcomeSection}>
            <View style={styles.userProfile}>
              <View style={styles.avatarContainer}>
                <LinearGradient
                  colors={[currentLevel.color, currentLevel.color + '80'] as any}
                  style={styles.avatarGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <Text style={styles.avatarIcon}>{currentLevel.icon}</Text>
                </LinearGradient>
                <View style={styles.levelBadge}>
                  <Text style={styles.levelText}>{userStats.level}</Text>
                </View>
              </View>
              <View style={styles.userInfo}>
                <Text style={styles.greeting}>{getTimeGreeting()}, {userName}!</Text>
                <Text style={styles.subGreeting}>Ready for your next adventure?</Text>
                <View style={styles.statsRow}>
                  <View style={styles.statItem}>
                    <Text style={styles.statValue}>{userStats.streak}</Text>
                    <Text style={styles.statLabel}>Day Streak</Text>
                  </View>
                  <View style={styles.statItem}>
                    <Text style={styles.statValue}>{userStats.totalXP}</Text>
                    <Text style={styles.statLabel}>Total XP</Text>
                  </View>
                  <View style={styles.statItem}>
                    <Text style={styles.statValue}>{userStats.questsCompleted}</Text>
                    <Text style={styles.statLabel}>Completed</Text>
                  </View>
                </View>
              </View>
            </View>
          </View>
          
          <LinearGradient
            colors={['rgba(255,255,255,0.2)', 'rgba(255,255,255,0.1)'] as any}
            style={styles.achievementTeaser}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          >
            <Text style={styles.achievementText}>
              🔥 You're ahead of 73% of explorers!
            </Text>
          </LinearGradient>
        </LinearGradient>

        <View style={styles.progressSection}>
          <ProgressDonut
            progress={userStats.totalBadges}
            total={userStats.maxBadges}
            title="Badges Unlocked"
            color={theme.colors.primaryAccent}
          />
        </View>

        <View style={styles.questSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Quest Spotlight</Text>
            <TouchableOpacity>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.questCarousel}
            decelerationRate="fast"
            snapToInterval={width * 0.75}
            snapToAlignment="start"
          >
            {mockQuests.map((quest) => (
              <QuestCard
                key={quest.id}
                {...quest}
                onPress={() => handleQuestPress(quest.id, quest.title)}
              />
            ))}
          </ScrollView>
        </View>

        <View style={styles.mapSection}>
          <LinearGradient
            colors={[theme.colors.surface, theme.colors.surfaceElevated] as any}
            style={styles.mapCard}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
          >
            <View style={styles.mapHeader}>
              <Text style={styles.mapTitle}>Nearby Quests</Text>
              <TouchableOpacity onPress={handleMapPress} style={styles.viewMapButton}>
                <LinearGradient
                  colors={theme.gradients.primary as any}
                  style={styles.viewMapGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                >
                  <Text style={styles.viewMapText}>View Map</Text>
                </LinearGradient>
              </TouchableOpacity>
            </View>
            <View style={styles.mapPreview}>
              <Text style={styles.mapPlaceholder}>🗺️ Interactive map preview</Text>
              <Text style={styles.mapSubtext}>
                5 active quests within 2km
              </Text>
              <View style={styles.mapPins}>
                <View style={[styles.mapPin, { backgroundColor: theme.colors.success }]} />
                <View style={[styles.mapPin, { backgroundColor: theme.colors.warning }]} />
                <View style={[styles.mapPin, { backgroundColor: theme.colors.error }]} />
                <View style={[styles.mapPin, { backgroundColor: theme.colors.accent1 }]} />
                <View style={[styles.mapPin, { backgroundColor: theme.colors.accent2 }]} />
              </View>
            </View>
          </LinearGradient>
        </View>

        <View style={styles.socialSection}>
          <Text style={styles.sectionTitle}>Recent Achievements</Text>
          <LinearGradient
            colors={[theme.colors.surface, theme.colors.surfaceElevated] as any}
            style={styles.socialCard}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
          >
            {recentAchievements.map((achievement, index) => (
              <View key={index} style={styles.achievementItem}>
                <LinearGradient
                  colors={[theme.colors.success + '20', theme.colors.success + '10'] as any}
                  style={styles.achievementGlow}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                >
                  <Text style={styles.achievementItemText}>{achievement}</Text>
                </LinearGradient>
              </View>
            ))}
          </LinearGradient>
        </View>

        <View style={styles.socialSection}>
          <Text style={styles.sectionTitle}>Community Activity</Text>
          <LinearGradient
            colors={[theme.colors.surface, theme.colors.surfaceElevated] as any}
            style={styles.socialCard}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
          >
            {socialFeed.map((activity, index) => (
              <View key={index} style={styles.socialItem}>
                <Text style={styles.socialItemText}>{activity}</Text>
              </View>
            ))}
          </LinearGradient>
          <LinearGradient
            colors={theme.gradients.secondary as any}
            style={styles.fomoContainer}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <Text style={styles.fomoText}>
              💨 Don't let them get ahead! Start a quest now!
            </Text>
          </LinearGradient>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}