import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../App';
import { lightTheme } from '../config/theme';

type ForgotPasswordScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'ForgotPassword'>;

export default function ForgotPasswordScreen() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const navigation = useNavigation<ForgotPasswordScreenNavigationProp>();

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleResetPassword = async () => {
    if (!email.trim()) {
      Alert.alert('Email Required', 'Please enter your email address.');
      return;
    }

    if (!validateEmail(email)) {
      Alert.alert('Invalid Email', 'Please enter a valid email address.');
      return;
    }

    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      setEmailSent(true);
    }, 1000);
  };

  const handleBackToLogin = () => {
    navigation.goBack();
  };

  const handleTryAgain = () => {
    setEmailSent(false);
    setEmail('');
  };

  if (emailSent) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.successContainer}>
          <View style={styles.successIcon}>
            <Text style={styles.successIconText}>📧</Text>
          </View>
          
          <Text style={styles.successTitle}>Check Your Inbox</Text>
          <Text style={styles.successSubtitle}>
            We've sent a password reset link to{'\n'}{email}
          </Text>
          
          <View style={styles.successMessage}>
            <Text style={styles.successMessageText}>
              Check your inbox—you'll unlock your world in seconds.
            </Text>
          </View>

          <TouchableOpacity
            style={styles.primaryButton}
            onPress={handleBackToLogin}
          >
            <Text style={styles.primaryButtonText}>Back to Sign In</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.secondaryButton}
            onPress={handleTryAgain}
          >
            <Text style={styles.secondaryButtonText}>Try Different Email</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.header}>
          <Text style={styles.title}>Reset Password</Text>
          <Text style={styles.subtitle}>
            Enter your email address and we'll send you a link to reset your password
          </Text>
        </View>

        <View style={styles.form}>
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Email Address</Text>
            <TextInput
              style={styles.input}
              placeholder="<EMAIL>"
              placeholderTextColor={lightTheme.colors.textSecondary}
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
              editable={!isLoading}
            />
          </View>

          <TouchableOpacity
            style={[styles.resetButton, isLoading && styles.buttonDisabled]}
            onPress={handleResetPassword}
            disabled={isLoading}
          >
            <Text style={styles.resetButtonText}>
              {isLoading ? 'Sending...' : 'Send Reset Link'}
            </Text>
          </TouchableOpacity>

          <View style={styles.infoContainer}>
            <Text style={styles.infoText}>
              Remember your password?{' '}
              <Text style={styles.infoLink} onPress={handleBackToLogin}>
                Sign In
              </Text>
            </Text>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: lightTheme.colors.background,
  },
  keyboardContainer: {
    flex: 1,
    paddingHorizontal: lightTheme.spacing.lg,
  },
  header: {
    alignItems: 'center',
    marginTop: lightTheme.spacing.xxl,
    marginBottom: lightTheme.spacing.xl,
  },
  title: {
    ...lightTheme.typography.h1,
    color: lightTheme.colors.textPrimary,
    textAlign: 'center',
    marginBottom: lightTheme.spacing.sm,
  },
  subtitle: {
    ...lightTheme.typography.body1,
    color: lightTheme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  form: {
    flex: 1,
    justifyContent: 'center',
  },
  inputContainer: {
    marginBottom: lightTheme.spacing.lg,
  },
  inputLabel: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textPrimary,
    marginBottom: lightTheme.spacing.sm,
    fontWeight: '600',
  },
  input: {
    backgroundColor: lightTheme.colors.surface,
    borderRadius: lightTheme.borderRadius.lg,
    paddingHorizontal: lightTheme.spacing.md,
    paddingVertical: lightTheme.spacing.md,
    fontSize: 16,
    color: lightTheme.colors.textPrimary,
    borderWidth: 2,
    borderColor: 'transparent',
    ...lightTheme.shadows.small,
  },
  resetButton: {
    backgroundColor: lightTheme.colors.primaryAccent,
    borderRadius: lightTheme.borderRadius.lg,
    paddingVertical: lightTheme.spacing.md,
    alignItems: 'center',
    marginBottom: lightTheme.spacing.lg,
    ...lightTheme.shadows.medium,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  resetButtonText: {
    ...lightTheme.typography.button,
    color: lightTheme.colors.white,
  },
  infoContainer: {
    alignItems: 'center',
    paddingHorizontal: lightTheme.spacing.md,
  },
  infoText: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textSecondary,
    textAlign: 'center',
  },
  infoLink: {
    color: lightTheme.colors.primaryAccent,
    fontWeight: '600',
  },
  successContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: lightTheme.spacing.lg,
  },
  successIcon: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: lightTheme.colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: lightTheme.spacing.xl,
    ...lightTheme.shadows.large,
  },
  successIconText: {
    fontSize: 60,
  },
  successTitle: {
    ...lightTheme.typography.h1,
    color: lightTheme.colors.textPrimary,
    textAlign: 'center',
    marginBottom: lightTheme.spacing.sm,
  },
  successSubtitle: {
    ...lightTheme.typography.body1,
    color: lightTheme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: lightTheme.spacing.xl,
    lineHeight: 24,
  },
  successMessage: {
    backgroundColor: lightTheme.colors.surface,
    borderRadius: lightTheme.borderRadius.lg,
    padding: lightTheme.spacing.lg,
    marginBottom: lightTheme.spacing.xl,
    ...lightTheme.shadows.small,
  },
  successMessageText: {
    ...lightTheme.typography.body1,
    color: lightTheme.colors.textPrimary,
    textAlign: 'center',
    lineHeight: 24,
  },
  primaryButton: {
    backgroundColor: lightTheme.colors.primaryAccent,
    borderRadius: lightTheme.borderRadius.lg,
    paddingVertical: lightTheme.spacing.md,
    paddingHorizontal: lightTheme.spacing.xl,
    alignItems: 'center',
    marginBottom: lightTheme.spacing.md,
    minWidth: 200,
    ...lightTheme.shadows.medium,
  },
  primaryButtonText: {
    ...lightTheme.typography.button,
    color: lightTheme.colors.white,
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderRadius: lightTheme.borderRadius.lg,
    paddingVertical: lightTheme.spacing.md,
    paddingHorizontal: lightTheme.spacing.xl,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: lightTheme.colors.primaryAccent,
    minWidth: 200,
  },
  secondaryButtonText: {
    ...lightTheme.typography.button,
    color: lightTheme.colors.primaryAccent,
  },
});