import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { lightTheme } from '../config/theme';
import { useTheme } from '../contexts/ThemeContext';
import { VALID_IONICONS, isValidIonicon, getIconType } from '../utils/iconUtils';

interface BadgeIconPickerProps {
  selectedIcon: string;
  onIconSelect: (icon: string) => void;
  style?: any;
}

// Comprehensive list of emojis for badges
const EMOJI_CATEGORIES = {
  'Nature & Places': [
    '🏔️', '🏕️', '🏖️', '🏜️', '🏝️', '🌋', '⛰️', '🏞️', '🌲', '🌳', '🌴', '🌵', '🌿', '☘️', '🍀', '🎋', '🌾', '🌱', '🌷', '🌸', '🌹', '🌺', '🌻', '🌼', '🌽', '🍄', '🌰', '🦋', '🐝', '🐞', '🦉', '🦅', '🐺', '🦊', '🐸', '🐢', '🐙', '🐠', '🐟', '🦈', '🐬'
  ],
  'Sports & Activities': [
    '⚽', '🏀', '🏈', '⚾', '🎾', '🏐', '🏉', '🥎', '🎱', '🏓', '🏸', '🥅', '🏒', '🏑', '🥍', '🏏', '⛳', '🏹', '🎣', '🥊', '🥋', '🎽', '🛹', '🛷', '⛸️', '🥌', '🎿', '⛷️', '🏂', '🪂', '🏋️', '🤼', '🤸', '⛹️', '🤺', '🏌️', '🏇', '🧘', '🏃', '🚴', '🏊'
  ],
  'Travel & Transport': [
    '🚗', '🚕', '🚙', '🚌', '🚎', '🏎️', '🚓', '🚑', '🚒', '🚐', '🛻', '🚚', '🚛', '🚜', '🏍️', '🛵', '🚲', '🛴', '🛺', '🚁', '🛸', '✈️', '🛩️', '🚀', '🛰️', '💺', '⛵', '🚤', '🛥️', '🛳️', '⛴️', '🚢', '⚓', '🚧', '⛽', '🚥', '🚦', '🗺️', '🗿', '🗽', '🗼', '🏰', '🏯', '🏟️', '🎡', '🎢', '🎠'
  ],
  'Food & Drinks': [
    '🍎', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🍈', '🍒', '🍑', '🥭', '🍍', '🥥', '🥝', '🍅', '🍆', '🥑', '🥦', '🥬', '🥒', '🌶️', '🫑', '🌽', '🥕', '🧄', '🧅', '🥔', '🍠', '🥐', '🥖', '🍞', '🥨', '🥯', '🧀', '🥚', '🍳', '🧈', '🥞', '🧇', '🥓', '🍗', '🍖', '🌭', '🍔', '🍟', '🍕'
  ],
  'Objects & Tools': [
    '⚔️', '🏹', '🛡️', '🔧', '🔨', '⚒️', '🛠️', '⛏️', '🔩', '⚙️', '🧰', '🧲', '🔫', '💣', '🧨', '🔪', '🗡️', '⚖️', '🔐', '🔒', '🔓', '🗝️', '🔑', '🔨', '⚒️', '🛠️', '⛏️', '🔧', '🔩', '⚙️', '⛓️', '🪓', '🪚', '🪜', '🪣', '🪝', '🧯', '🛢️', '🛞'
  ],
  'Achievement & Success': [
    '🏆', '🥇', '🥈', '🥉', '🏅', '🎖️', '👑', '💎', '💰', '💯', '⭐', '🌟', '✨', '🎯', '🎪', '🎭', '🎨', '🎬', '🎤', '🎧', '🎼', '🎵', '🎶', '🎹', '🥁', '🎷', '🎺', '🎸', '🪕', '🎻', '🔥', '💥', '⚡', '🌈', '☀️', '⭐', '🌙', '🪐', '🌌', '🌠', '☄️'
  ],
  'Science & Technology': [
    '🔬', '🔭', '📡', '🛰️', '🧪', '🧬', '🦠', '💊', '🩺', '🩹', '🔋', '🔌', '💡', '🔦', '🕯️', '🪔', '📱', '💻', '🖥️', '⌨️', '🖱️', '🖨️', '📀', '💾', '💿', '📼', '📷', '📸', '📹', '🎥', '📽️', '🎞️', '📞', '☎️', '📠', '📺', '📻', '🎙️', '⏰', '⏲️', '⏱️', '🕰️'
  ]
};


export default function BadgeIconPicker({ selectedIcon, onIconSelect, style }: BadgeIconPickerProps) {
  const { theme } = useTheme();
  const [modalVisible, setModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('Achievement & Success');
  const [iconType, setIconType] = useState<'emoji' | 'ionicon'>('emoji');

  const getFilteredEmojis = () => {
    const categoryEmojis = EMOJI_CATEGORIES[selectedCategory as keyof typeof EMOJI_CATEGORIES] || [];
    if (!searchQuery) return categoryEmojis;
    return categoryEmojis.filter(emoji => 
      emoji.includes(searchQuery) || 
      selectedCategory.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  const getFilteredIonicons = () => {
    if (!searchQuery) return VALID_IONICONS;
    return VALID_IONICONS.filter(icon => 
      icon.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  const renderEmojiGrid = () => {
    const emojis = getFilteredEmojis();
    return (
      <ScrollView style={styles.iconGrid} showsVerticalScrollIndicator={false}>
        <View style={styles.gridContainer}>
          {emojis.map((emoji, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.iconItem,
                selectedIcon === emoji && styles.iconItemSelected
              ]}
              onPress={() => {
                onIconSelect(emoji);
                setModalVisible(false);
              }}
            >
              <Text style={styles.emojiIcon}>{emoji}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    );
  };

  const renderIoniconGrid = () => {
    const icons = getFilteredIonicons();
    return (
      <ScrollView style={styles.iconGrid} showsVerticalScrollIndicator={false}>
        <View style={styles.gridContainer}>
          {icons.map((iconName, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.iconItem,
                selectedIcon === iconName && styles.iconItemSelected
              ]}
              onPress={() => {
                onIconSelect(iconName);
                setModalVisible(false);
              }}
            >
              <Ionicons name={iconName as any} size={24} color={theme.colors.textPrimary} />
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    );
  };

  const renderSelectedIcon = () => {
    if (!selectedIcon) {
      return (
        <View style={styles.placeholderIcon}>
          <Ionicons name="add-outline" size={24} color={theme.colors.textSecondary} />
        </View>
      );
    }

    const iconType = getIconType(selectedIcon);
    
    if (iconType === 'ionicon') {
      return <Ionicons name={selectedIcon as any} size={32} color={theme.colors.primaryAccent} />
    } else if (iconType === 'emoji') {
      return <Text style={styles.selectedEmojiDisplay}>{selectedIcon}</Text>;
    } else {
      // Fallback for unknown icons - show as text
      return <Text style={styles.selectedEmojiDisplay}>{selectedIcon}</Text>;
    }
  };

  const styles = createStyles(theme);

  return (
    <View style={[styles.container, style]}>
      <Text style={styles.label}>Badge Icon</Text>
      <TouchableOpacity
        style={styles.iconSelector}
        onPress={() => setModalVisible(true)}
      >
        <View style={styles.iconDisplay}>
          {renderSelectedIcon()}
        </View>
        <Text style={styles.selectText}>
          {selectedIcon ? 'Change Icon' : 'Select Icon'}
        </Text>
        <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setModalVisible(false)}>
              <Ionicons name="close" size={24} color={theme.colors.textPrimary} />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Select Badge Icon</Text>
            <View style={{ width: 24 }} />
          </View>

          {/* Icon Type Selector */}
          <View style={styles.typeSelector}>
            <TouchableOpacity
              style={[
                styles.typeButton,
                iconType === 'emoji' && styles.typeButtonActive
              ]}
              onPress={() => setIconType('emoji')}
            >
              <Text style={[
                styles.typeButtonText,
                iconType === 'emoji' && styles.typeButtonTextActive
              ]}>
                Emojis
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.typeButton,
                iconType === 'ionicon' && styles.typeButtonActive
              ]}
              onPress={() => setIconType('ionicon')}
            >
              <Text style={[
                styles.typeButtonText,
                iconType === 'ionicon' && styles.typeButtonTextActive
              ]}>
                Icons
              </Text>
            </TouchableOpacity>
          </View>

          {/* Search Bar */}
          <View style={styles.searchContainer}>
            <Ionicons name="search-outline" size={20} color={theme.colors.textSecondary} />
            <TextInput
              style={styles.searchInput}
              placeholder={iconType === 'emoji' ? 'Search emojis...' : 'Search icons...'}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
          </View>

          {/* Category Selector for Emojis */}
          {iconType === 'emoji' && (
            <ScrollView 
              horizontal 
              showsHorizontalScrollIndicator={false}
              style={styles.categoryContainer}
            >
              {Object.keys(EMOJI_CATEGORIES).map((category) => (
                <TouchableOpacity
                  key={category}
                  style={[
                    styles.categoryButton,
                    selectedCategory === category && styles.categoryButtonActive
                  ]}
                  onPress={() => setSelectedCategory(category)}
                >
                  <Text style={[
                    styles.categoryButtonText,
                    selectedCategory === category && styles.categoryButtonTextActive
                  ]}>
                    {category}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          )}

          {/* Icon Grid */}
          {iconType === 'emoji' ? renderEmojiGrid() : renderIoniconGrid()}
        </View>
      </Modal>
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.textPrimary,
    marginBottom: 8,
  },
  iconSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: theme.colors.borderLight,
  },
  iconDisplay: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: theme.colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  placeholderIcon: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedEmojiDisplay: {
    fontSize: 28,
  },
  selectText: {
    flex: 1,
    fontSize: 16,
    color: theme.colors.textPrimary,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.borderLight,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.textPrimary,
  },
  typeSelector: {
    flexDirection: 'row',
    margin: 16,
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 4,
  },
  typeButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
  },
  typeButtonActive: {
    backgroundColor: theme.colors.primaryAccent,
  },
  typeButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.textSecondary,
  },
  typeButtonTextActive: {
    color: 'white',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 16,
    marginTop: 0,
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: theme.colors.borderLight,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    color: theme.colors.textPrimary,
  },
  categoryContainer: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: theme.colors.surface,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
    borderColor: theme.colors.borderLight,
  },
  categoryButtonActive: {
    backgroundColor: theme.colors.primaryAccent,
    borderColor: theme.colors.primaryAccent,
  },
  categoryButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: theme.colors.textSecondary,
  },
  categoryButtonTextActive: {
    color: 'white',
  },
  iconGrid: {
    flex: 1,
    paddingHorizontal: 16,
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  iconItem: {
    width: '18%',
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: theme.colors.borderLight,
  },
  iconItemSelected: {
    backgroundColor: theme.colors.primaryAccent + '20',
    borderColor: theme.colors.primaryAccent,
  },
  emojiIcon: {
    fontSize: 24,
  },
});