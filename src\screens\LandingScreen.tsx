import React, { useRef, useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ImageBackground,
  Animated,
  ScrollView,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../App';
import { useTheme } from '../contexts/ThemeContext';

const { width, height } = Dimensions.get('window');

type LandingScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'Landing'
>;

interface LandingScreenProps {
  navigation: LandingScreenNavigationProp;
}

const getCarouselData = (theme: any) => [
  {
    id: '1',
    title: 'Discover Hidden Gems',
    subtitle: 'Explore your city like never before',
    image: 'https://images.unsplash.com/photo-1519501025264-65ba15a82390?w=800&h=600&fit=crop',
    gradient: [theme.colors.accent1, theme.colors.accent2],
  },
  {
    id: '2',
    title: 'Collect Epic Badges',
    subtitle: 'Unlock achievements and show off your progress',
    image: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=800&h=600&fit=crop',
    gradient: [theme.colors.accent3, theme.colors.accent4],
  },
  {
    id: '3',
    title: 'Adventure Awaits',
    subtitle: 'Join thousands of explorers worldwide',
    image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop',
    gradient: [theme.colors.accent5, theme.colors.primaryAccent],
  },
];

export default function LandingScreen({ navigation }: LandingScreenProps) {
  const { theme } = useTheme();
  const [currentIndex, setCurrentIndex] = useState(0);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const flatListRef = useRef<FlatList>(null);
  const carouselData = getCarouselData(theme);
  const styles = createStyles(theme);

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();

    // Auto-scroll carousel
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => {
        const nextIndex = (prevIndex + 1) % carouselData.length;
        flatListRef.current?.scrollToIndex({ index: nextIndex, animated: true });
        return nextIndex;
      });
    }, 4000);

    return () => clearInterval(interval);
  }, [fadeAnim, slideAnim]);

  const renderCarouselItem = ({ item }: { item: typeof carouselData[0] }) => (
    <View style={[styles.carouselItem, { width }]}>
      <ImageBackground
        source={{ uri: item.image }}
        style={styles.carouselImage}
        resizeMode="cover"
      >
        <LinearGradient
          colors={['rgba(0,0,0,0.3)', 'rgba(0,0,0,0.7)']}
          style={styles.carouselOverlay}
        >
          <Animated.View
            style={[
              styles.carouselContent,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            <Text style={styles.carouselTitle}>{item.title}</Text>
            <Text style={styles.carouselSubtitle}>{item.subtitle}</Text>
          </Animated.View>
        </LinearGradient>
      </ImageBackground>
    </View>
  );

  const renderDot = (index: number) => (
    <View
      key={index}
      style={[
        styles.dot,
        {
          backgroundColor: currentIndex === index 
            ? theme.colors.white 
            : 'rgba(255, 255, 255, 0.5)',
          width: currentIndex === index ? 24 : 8,
        },
      ]}
    />
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Top Carousel Section */}
      <View style={styles.carouselContainer}>
        <FlatList
          ref={flatListRef}
          data={carouselData}
          renderItem={renderCarouselItem}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onMomentumScrollEnd={(event) => {
            const index = Math.round(event.nativeEvent.contentOffset.x / width);
            setCurrentIndex(index);
          }}
          keyExtractor={(item) => item.id}
        />
        
        {/* Dot Indicators */}
        <View style={styles.dotContainer}>
          {carouselData.map((_, index) => renderDot(index))}
        </View>
      </View>

      {/* Bottom Action Section */}
      <Animated.View
        style={[
          styles.bottomSection,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          },
        ]}
      >
        <LinearGradient
          colors={[theme.colors.surface, theme.colors.surfaceElevated]}
          style={styles.bottomContent}
        >
          <View style={styles.brandingSection}>
            <Text style={styles.appName}>MCQs Explorer</Text>
            <Text style={styles.tagline}>
              Transform your city into an epic adventure
            </Text>
          </View>

          <View style={styles.statsSection}>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>10K+</Text>
              <Text style={styles.statLabel}>Explorers</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>500+</Text>
              <Text style={styles.statLabel}>Quests</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>50+</Text>
              <Text style={styles.statLabel}>Cities</Text>
            </View>
          </View>

          <View style={styles.buttonSection}>
            <TouchableOpacity
              style={styles.primaryButtonContainer}
              onPress={() => navigation.navigate('RegisterEmail')}
            >
              <LinearGradient
                colors={theme.gradients.primary}
                style={styles.primaryButton}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                <Text style={styles.primaryButtonText}>🚀 Start Your Journey</Text>
              </LinearGradient>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.secondaryButton}
              onPress={() => navigation.navigate('Login')}
            >
              <Text style={styles.secondaryButtonText}>Already an Explorer?</Text>
            </TouchableOpacity>
          </View>
        </LinearGradient>
      </Animated.View>
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  carouselContainer: {
    flex: 1,
    position: 'relative',
  },
  carouselItem: {
    height: height * 0.6,
  },
  carouselImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  carouselOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.xl,
  },
  carouselContent: {
    alignItems: 'center',
  },
  carouselTitle: {
    ...theme.typography.display1,
    color: theme.colors.white,
    textAlign: 'center',
    marginBottom: theme.spacing.md,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 6,
  },
  carouselSubtitle: {
    ...theme.typography.h6,
    color: theme.colors.white,
    textAlign: 'center',
    opacity: 0.9,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 4,
  },
  dotContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    bottom: 20,
    left: 0,
    right: 0,
    gap: 8,
  },
  dot: {
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    ...theme.shadows.small,
  },
  bottomSection: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderTopLeftRadius: theme.borderRadius.xxxl,
    borderTopRightRadius: theme.borderRadius.xxxl,
    overflow: 'hidden',
    ...theme.shadows.large,
  },
  bottomContent: {
    padding: theme.spacing.xl,
    paddingBottom: theme.spacing.xxl,
  },
  brandingSection: {
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
  },
  appName: {
    ...theme.typography.display2,
    color: theme.colors.textPrimary,
    fontWeight: '800',
    marginBottom: theme.spacing.sm,
  },
  tagline: {
    ...theme.typography.body1,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  statsSection: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: theme.spacing.xl,
    paddingHorizontal: theme.spacing.lg,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    ...theme.typography.h4,
    color: theme.colors.primaryAccent,
    fontWeight: '800',
  },
  statLabel: {
    ...theme.typography.caption,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.xs,
  },
  buttonSection: {
    gap: theme.spacing.md,
  },
  primaryButtonContainer: {
    borderRadius: theme.borderRadius.xl,
    overflow: 'hidden',
    ...theme.shadows.glow,
  },
  primaryButton: {
    paddingVertical: theme.spacing.lg,
    paddingHorizontal: theme.spacing.xl,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 56,
  },
  primaryButtonText: {
    ...theme.typography.button,
    color: theme.colors.white,
    fontWeight: '700',
    fontSize: 16,
  },
  secondaryButton: {
    paddingVertical: theme.spacing.lg,
    paddingHorizontal: theme.spacing.xl,
    borderRadius: theme.borderRadius.xl,
    borderWidth: 2,
    borderColor: theme.colors.primaryAccent,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 56,
    backgroundColor: 'transparent',
  },
  secondaryButtonText: {
    ...theme.typography.button,
    color: theme.colors.primaryAccent,
    fontWeight: '600',
    fontSize: 16,
  },
});