import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDocs, 
  getDoc, 
  query, 
  where, 
  orderBy, 
  Timestamp 
} from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { db, storage } from '../config/firebase';

export interface BadgeCoords {
  lat: number;
  lng: number;
}

export interface Badge {
  id?: string;
  title: string;
  clue: string;
  story: string;
  imageUrl?: string;
  audioUrl?: string;
  category: string;
  unlockMethod: 'gps' | 'code';
  coords: BadgeCoords;
  code?: string;
  icon: string;
  createdBy: string;
  createdAt: string;
  city: string[];
  state: string;
  active: boolean;
  difficulty: string;
  xp: number;
}

export interface BadgeFormData {
  title: string;
  clue: string;
  story: string;
  category: string;
  unlockMethod: 'gps' | 'code';
  coords: BadgeCoords;
  code?: string;
  icon: string;
  city: string[];
  state: string;
}

export const BADGE_CATEGORIES = [
  { id: 'temple', name: 'Temple', icon: 'temple-hindu' },
  { id: 'nature', name: 'Nature', icon: 'nature' },
  { id: 'urban', name: 'Urban', icon: 'location-city' },
  { id: 'historical', name: 'Historical', icon: 'account-balance' },
  { id: 'cultural', name: 'Cultural', icon: 'palette' },
  { id: 'food', name: 'Food', icon: 'restaurant' },
  { id: 'shopping', name: 'Shopping', icon: 'shopping-bag' },
  { id: 'entertainment', name: 'Entertainment', icon: 'movie' },
  { id: 'sports', name: 'Sports', icon: 'sports-soccer' },
  { id: 'education', name: 'Education', icon: 'school' },
];

export const INDIAN_STATES = [
  'Andhra Pradesh', 'Arunachal Pradesh', 'Assam', 'Bihar', 'Chhattisgarh',
  'Goa', 'Gujarat', 'Haryana', 'Himachal Pradesh', 'Jharkhand', 'Karnataka',
  'Kerala', 'Madhya Pradesh', 'Maharashtra', 'Manipur', 'Meghalaya', 'Mizoram',
  'Nagaland', 'Odisha', 'Punjab', 'Rajasthan', 'Sikkim', 'Tamil Nadu',
  'Telangana', 'Tripura', 'Uttar Pradesh', 'Uttarakhand', 'West Bengal',
  'Delhi', 'Jammu and Kashmir', 'Ladakh', 'Puducherry', 'Chandigarh',
  'Dadra and Nagar Haveli and Daman and Diu', 'Lakshadweep', 'Andaman and Nicobar Islands'
];

export const CITIES_BY_STATE: Record<string, string[]> = {
  'Maharashtra': ['Mumbai', 'Pune', 'Nagpur', 'Nashik', 'Aurangabad', 'Solapur', 'Kolhapur', 'Thane', 'Navi Mumbai'],
  'Karnataka': ['Bangalore', 'Mysore', 'Hubli', 'Mangalore', 'Belgaum', 'Gulbarga', 'Davanagere', 'Bellary'],
  'Tamil Nadu': ['Chennai', 'Coimbatore', 'Madurai', 'Tiruchirappalli', 'Salem', 'Tirunelveli', 'Erode', 'Vellore'],
  'Uttar Pradesh': ['Lucknow', 'Kanpur', 'Ghaziabad', 'Agra', 'Varanasi', 'Meerut', 'Allahabad', 'Bareilly'],
  'Gujarat': ['Ahmedabad', 'Surat', 'Vadodara', 'Rajkot', 'Bhavnagar', 'Jamnagar', 'Junagadh', 'Gandhinagar'],
  'West Bengal': ['Kolkata', 'Howrah', 'Durgapur', 'Asansol', 'Siliguri', 'Darjeeling', 'Kharagpur', 'Haldia'],
  'Rajasthan': ['Jaipur', 'Jodhpur', 'Udaipur', 'Kota', 'Bikaner', 'Ajmer', 'Bharatpur', 'Alwar'],
  'Madhya Pradesh': ['Bhopal', 'Indore', 'Gwalior', 'Jabalpur', 'Ujjain', 'Sagar', 'Dewas', 'Satna'],
  'Andhra Pradesh': ['Visakhapatnam', 'Vijayawada', 'Guntur', 'Nellore', 'Kurnool', 'Rajahmundry', 'Tirupati', 'Kakinada'],
  'Telangana': ['Hyderabad', 'Warangal', 'Nizamabad', 'Karimnagar', 'Ramagundam', 'Khammam', 'Mahbubnagar', 'Nalgonda'],
  'Kerala': ['Thiruvananthapuram', 'Kochi', 'Kozhikode', 'Thrissur', 'Alappuzha', 'Kollam', 'Palakkad', 'Kannur'],
  'Punjab': ['Chandigarh', 'Ludhiana', 'Amritsar', 'Jalandhar', 'Patiala', 'Bathinda', 'Mohali', 'Pathankot'],
  'Haryana': ['Gurgaon', 'Faridabad', 'Panipat', 'Ambala', 'Yamunanagar', 'Rohtak', 'Hisar', 'Karnal'],
  'Bihar': ['Patna', 'Gaya', 'Bhagalpur', 'Muzaffarpur', 'Purnia', 'Darbhanga', 'Bihar Sharif', 'Arrah'],
  'Odisha': ['Bhubaneswar', 'Cuttack', 'Rourkela', 'Brahmapur', 'Sambalpur', 'Puri', 'Balasore', 'Baripada'],
  'Jharkhand': ['Ranchi', 'Jamshedpur', 'Dhanbad', 'Bokaro', 'Deoghar', 'Phusro', 'Hazaribagh', 'Giridih'],
  'Assam': ['Guwahati', 'Silchar', 'Dibrugarh', 'Jorhat', 'Nagaon', 'Tinsukia', 'Tezpur', 'Diphu'],
  'Chhattisgarh': ['Raipur', 'Bhilai', 'Bilaspur', 'Korba', 'Durg', 'Raigarh', 'Rajnandgaon', 'Jagdalpur'],
  'Uttarakhand': ['Dehradun', 'Haridwar', 'Roorkee', 'Haldwani', 'Rudrapur', 'Kashipur', 'Rishikesh', 'Kotdwar'],
  'Himachal Pradesh': ['Shimla', 'Dharamshala', 'Solan', 'Mandi', 'Palampur', 'Baddi', 'Nahan', 'Paonta Sahib'],
  'Jammu and Kashmir': ['Srinagar', 'Jammu', 'Baramulla', 'Anantnag', 'Sopore', 'Kathua', 'Udhampur', 'Poonch'],
  'Goa': ['Panaji', 'Margao', 'Vasco da Gama', 'Mapusa', 'Ponda', 'Bicholim', 'Curchorem', 'Sanquelim'],
  'Tripura': ['Agartala', 'Dharmanagar', 'Udaipur', 'Kailashahar', 'Belonia', 'Khowai', 'Pratapgarh', 'Ranirbazar'],
  'Manipur': ['Imphal', 'Thoubal', 'Bishnupur', 'Churachandpur', 'Kakching', 'Ukhrul', 'Senapati', 'Tamenglong'],
  'Meghalaya': ['Shillong', 'Tura', 'Jowai', 'Nongstoin', 'Baghmara', 'Ampati', 'Resubelpara', 'Williamnagar'],
  'Nagaland': ['Kohima', 'Dimapur', 'Mokokchung', 'Tuensang', 'Wokha', 'Zunheboto', 'Phek', 'Kiphire'],
  'Mizoram': ['Aizawl', 'Lunglei', 'Saiha', 'Champhai', 'Kolasib', 'Serchhip', 'Lawngtlai', 'Mamit'],
  'Arunachal Pradesh': ['Itanagar', 'Naharlagun', 'Pasighat', 'Tezpur', 'Bomdila', 'Tawang', 'Ziro', 'Along'],
  'Sikkim': ['Gangtok', 'Namchi', 'Gyalshing', 'Mangan', 'Rangpo', 'Singtam', 'Jorethang', 'Nayabazar'],
  'Delhi': ['New Delhi', 'Delhi', 'Gurgaon', 'Faridabad', 'Ghaziabad', 'Noida', 'Greater Noida', 'Dwarka'],
};

class BadgeService {
  private readonly collection = collection(db, 'badges');

  async createBadge(badgeData: Omit<Badge, 'id'>): Promise<string> {
    try {
      const docRef = await addDoc(this.collection, badgeData);
      return docRef.id;
    } catch (error) {
      console.error('Error creating badge:', error);
      throw error;
    }
  }

  async updateBadge(badgeId: string, badgeData: Partial<Badge>): Promise<void> {
    try {
      const badgeRef = doc(db, 'badges', badgeId);
      await updateDoc(badgeRef, badgeData);
    } catch (error) {
      console.error('Error updating badge:', error);
      throw error;
    }
  }

  async deleteBadge(badgeId: string): Promise<void> {
    try {
      const badge = await this.getBadgeById(badgeId);
      
      // Delete associated image
      if (badge?.imageUrl) {
        await this.deleteBadgeImage(badge.imageUrl);
      }

      await deleteDoc(doc(db, 'badges', badgeId));
    } catch (error) {
      console.error('Error deleting badge:', error);
      throw error;
    }
  }

  async getBadgeById(badgeId: string): Promise<Badge | null> {
    try {
      const docRef = doc(db, 'badges', badgeId);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as Badge;
      }
      return null;
    } catch (error) {
      console.error('Error getting badge:', error);
      throw error;
    }
  }

  async getAllBadges(): Promise<Badge[]> {
    try {
      const q = query(this.collection, orderBy('createdAt', 'desc'));
      const querySnapshot = await getDocs(q);
      
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Badge[];
    } catch (error) {
      console.error('Error getting badges:', error);
      throw error;
    }
  }

  async getBadgesByCategory(category: string): Promise<Badge[]> {
    try {
      const q = query(
        this.collection,
        where('category', '==', category),
        orderBy('createdAt', 'desc')
      );
      const querySnapshot = await getDocs(q);
      
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Badge[];
    } catch (error) {
      console.error('Error getting badges by category:', error);
      throw error;
    }
  }

  async getBadgesByState(state: string): Promise<Badge[]> {
    try {
      const q = query(
        this.collection,
        where('state', '==', state),
        orderBy('createdAt', 'desc')
      );
      const querySnapshot = await getDocs(q);
      
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Badge[];
    } catch (error) {
      console.error('Error getting badges by state:', error);
      throw error;
    }
  }

  async getBadgesByCity(city: string): Promise<Badge[]> {
    try {
      const q = query(
        this.collection,
        where('city', 'array-contains', city),
        orderBy('createdAt', 'desc')
      );
      const querySnapshot = await getDocs(q);
      
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Badge[];
    } catch (error) {
      console.error('Error getting badges by city:', error);
      throw error;
    }
  }

  private async uploadBadgeImage(imageFile: any): Promise<string> {
    try {
      const filename = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      const storageRef = ref(storage, `badge-icons/${filename}`);
      
      const response = await fetch(imageFile.uri);
      const blob = await response.blob();
      
      await uploadBytes(storageRef, blob);
      return await getDownloadURL(storageRef);
    } catch (error) {
      console.error('Error uploading badge image:', error);
      throw error;
    }
  }

  private async deleteBadgeImage(imageUrl: string): Promise<void> {
    try {
      const storageRef = ref(storage, imageUrl);
      await deleteObject(storageRef);
    } catch (error) {
      console.error('Error deleting badge image:', error);
      // Don't throw here as the main operation (badge deletion) should continue
    }
  }

  getCitiesByState(state: string): string[] {
    return CITIES_BY_STATE[state] || [];
  }

  getCategories() {
    return BADGE_CATEGORIES;
  }

  getStates() {
    return INDIAN_STATES;
  }
}

export const badgeService = new BadgeService();

// Export functions for direct use
export const createBadge = (badgeData: Omit<Badge, 'id'>) => badgeService.createBadge(badgeData);
export const updateBadge = (badgeId: string, badgeData: Partial<Badge>) => badgeService.updateBadge(badgeId, badgeData);
export const deleteBadge = (badgeId: string) => badgeService.deleteBadge(badgeId);
export const getBadgeById = (badgeId: string) => badgeService.getBadgeById(badgeId);
export const getAllBadges = () => badgeService.getAllBadges();
export const getBadgesByCategory = (category: string) => badgeService.getBadgesByCategory(category);
export const getBadgesByState = (state: string) => badgeService.getBadgesByState(state);
export const getBadgesByCity = (city: string) => badgeService.getBadgesByCity(city);