import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { lightTheme, levelBadges, createGlowEffect } from '../config/theme';

interface ProgressDonutProps {
  progress: number;
  total: number;
  title: string;
  color?: string;
}

export default function ProgressDonut({ 
  progress, 
  total, 
  title, 
  color = lightTheme.colors.primaryAccent 
}: ProgressDonutProps) {
  const percentage = (progress / total) * 100;
  const completionRate = progress / total;
  
  const getProgressColor = () => {
    if (completionRate >= 0.9) return lightTheme.colors.success;
    if (completionRate >= 0.7) return lightTheme.colors.primaryAccent;
    if (completionRate >= 0.5) return lightTheme.colors.warning;
    return lightTheme.colors.secondaryAccent;
  };

  const getProgressGradient = () => {
    if (completionRate >= 0.9) return lightTheme.gradients.success;
    if (completionRate >= 0.7) return lightTheme.gradients.primary;
    if (completionRate >= 0.5) return lightTheme.gradients.secondary;
    return lightTheme.gradients.secondary;
  };

  const getMotivationMessage = () => {
    if (progress === total) return { text: '🎉 Elite Status Achieved!', color: lightTheme.colors.success };
    if (progress >= total * 0.8) return { text: '🔥 Almost there, champion!', color: lightTheme.colors.primaryAccent };
    if (progress >= total * 0.5) return { text: '💪 Keep climbing!', color: lightTheme.colors.warning };
    return { text: `⚡ ${total - progress} more to unlock Elite!`, color: lightTheme.colors.secondaryAccent };
  };

  const motivationMessage = getMotivationMessage();
  const progressGradient = getProgressGradient();

  return (
    <LinearGradient
      colors={[lightTheme.colors.surface, lightTheme.colors.surfaceElevated] as any}
      style={[styles.container, progress === total ? createGlowEffect(lightTheme.colors.success, 0.3) : {}]}
      start={{ x: 0, y: 0 }}
      end={{ x: 0, y: 1 }}
    >
      <View style={styles.chartContainer}>
        <View style={styles.progressRing}>
          <View style={[styles.progressBackground, { borderColor: lightTheme.colors.borderLight }]} />
          {/* Gradient progress fill */}
          <View style={styles.progressGradientContainer}>
            <LinearGradient
              colors={progressGradient as any}
              style={[
                styles.progressFill,
                { 
                  transform: [{ rotate: `${(percentage * 3.6) - 90}deg` }]
                }
              ]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            />
          </View>
          <View style={styles.progressInner} />
        </View>
        <View style={styles.centerContent}>
          <Text style={styles.progressText}>{progress}</Text>
          <Text style={styles.totalText}>of {total}</Text>
          <Text style={styles.percentageText}>{Math.round(percentage)}%</Text>
        </View>
      </View>
      <Text style={styles.title}>{title}</Text>
      <LinearGradient
        colors={[motivationMessage.color + '20', motivationMessage.color + '10'] as any}
        style={styles.achievementHint}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
      >
        <Text style={[styles.achievementText, { color: motivationMessage.color }]}>
          {motivationMessage.text}
        </Text>
      </LinearGradient>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    borderRadius: lightTheme.borderRadius.xxl,
    padding: lightTheme.spacing.xl,
    marginVertical: lightTheme.spacing.md,
    ...lightTheme.shadows.large,
  },
  chartContainer: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressRing: {
    width: 140,
    height: 140,
    position: 'relative',
  },
  progressBackground: {
    position: 'absolute',
    width: 140,
    height: 140,
    borderRadius: 70,
    borderWidth: 10,
    borderColor: lightTheme.colors.borderLight,
  },
  progressGradientContainer: {
    position: 'absolute',
    width: 140,
    height: 140,
    borderRadius: 70,
    overflow: 'hidden',
  },
  progressFill: {
    position: 'absolute',
    width: 140,
    height: 140,
    borderRadius: 70,
    borderWidth: 10,
    borderTopColor: 'transparent',
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: 'transparent',
  },
  progressInner: {
    position: 'absolute',
    top: 10,
    left: 10,
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: lightTheme.colors.surface,
    ...lightTheme.shadows.medium,
  },
  centerContent: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    width: 140,
    height: 140,
  },
  progressText: {
    ...lightTheme.typography.display2,
    color: lightTheme.colors.textPrimary,
  },
  totalText: {
    ...lightTheme.typography.caption,
    color: lightTheme.colors.textSecondary,
    marginTop: -2,
  },
  percentageText: {
    ...lightTheme.typography.overline,
    color: lightTheme.colors.textTertiary,
    marginTop: 2,
  },
  title: {
    ...lightTheme.typography.h5,
    color: lightTheme.colors.textPrimary,
    marginTop: lightTheme.spacing.lg,
    textAlign: 'center',
  },
  achievementHint: {
    marginTop: lightTheme.spacing.lg,
    paddingHorizontal: lightTheme.spacing.lg,
    paddingVertical: lightTheme.spacing.md,
    borderRadius: lightTheme.borderRadius.xl,
    ...lightTheme.shadows.small,
  },
  achievementText: {
    ...lightTheme.typography.buttonSmall,
    fontWeight: '700',
    textAlign: 'center',
  },
});