import { doc, setDoc, serverTimestamp, getDoc } from 'firebase/firestore';
import { db } from '../config/firebase';

export interface UnlockResult {
  success: boolean;
  alreadyUnlocked: boolean;
  message: string;
  viaHint?: boolean;
}

export class UnlockService {
  static async unlockBadge(
    userId: string,
    badgeId: string,
    viaHint: boolean = false
  ): Promise<UnlockResult> {
    try {
      const unlockedDocRef = doc(db, `users/${userId}/unlocked/${badgeId}`);
      
      // Check if already unlocked
      const existingDoc = await getDoc(unlockedDocRef);
      if (existingDoc.exists()) {
        return {
          success: true,
          alreadyUnlocked: true,
          message: 'Badge already unlocked!',
          viaHint: existingDoc.data().viaHint || false,
        };
      }

      // Unlock the badge
      await setDoc(unlockedDocRef, {
        unlockedAt: serverTimestamp(),
        viaHint,
      });

      return {
        success: true,
        alreadyUnlocked: false,
        message: viaHint 
          ? 'Badge unlocked with hint! ⭐ (Half star earned)'
          : 'Badge unlocked! 🌟 (Full star earned)',
        viaHint,
      };
    } catch (error) {
      console.error('Error unlocking badge:', error);
      return {
        success: false,
        alreadyUnlocked: false,
        message: 'Failed to unlock badge. Please try again.',
      };
    }
  }

  static async checkUnlockStatus(
    userId: string,
    badgeId: string
  ): Promise<{ isUnlocked: boolean; viaHint?: boolean; unlockedAt?: any }> {
    try {
      const unlockedDocRef = doc(db, `users/${userId}/unlocked/${badgeId}`);
      const unlockedDoc = await getDoc(unlockedDocRef);
      
      if (unlockedDoc.exists()) {
        const data = unlockedDoc.data();
        return {
          isUnlocked: true,
          viaHint: data.viaHint || false,
          unlockedAt: data.unlockedAt,
        };
      }
      
      return { isUnlocked: false };
    } catch (error) {
      console.error('Error checking unlock status:', error);
      return { isUnlocked: false };
    }
  }

  static async getUserStats(userId: string): Promise<{
    totalUnlocked: number;
    unlockedViaHint: number;
    fullStars: number;
    halfStars: number;
  }> {
    try {
      // In a real implementation, you might want to maintain a summary document
      // or use a cloud function to aggregate this data for performance
      
      // For now, we'll return mock data
      // In production, you'd query the unlocked collection and aggregate
      
      return {
        totalUnlocked: 5,
        unlockedViaHint: 2,
        fullStars: 3,
        halfStars: 2,
      };
    } catch (error) {
      console.error('Error getting user stats:', error);
      return {
        totalUnlocked: 0,
        unlockedViaHint: 0,
        fullStars: 0,
        halfStars: 0,
      };
    }
  }

  static calculateStarRating(totalUnlocked: number, unlockedViaHint: number): number {
    const fullStars = totalUnlocked - unlockedViaHint;
    const halfStars = unlockedViaHint;
    return fullStars + (halfStars * 0.5);
  }

  static getAchievementMessage(totalUnlocked: number): string {
    if (totalUnlocked === 0) return "Start your first adventure!";
    if (totalUnlocked < 5) return "Great start, keep exploring!";
    if (totalUnlocked < 10) return "You're becoming a true explorer!";
    if (totalUnlocked < 20) return "Impressive! The city has no secrets from you!";
    if (totalUnlocked < 50) return "Master Explorer! You know this city like the back of your hand!";
    return "Legendary Explorer! You've conquered the entire city!";
  }
}