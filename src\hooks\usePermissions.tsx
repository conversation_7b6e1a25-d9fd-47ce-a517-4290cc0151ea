import { useState, useEffect } from 'react';
import { Alert, Linking, Platform } from 'react-native';
import * as Location from 'expo-location';
import * as ImagePicker from 'expo-image-picker';
import * as MediaLibrary from 'expo-media-library';
import { AudioModule } from 'expo-audio';
import * as FileSystem from 'expo-file-system';

interface PermissionStatus {
  location: boolean;
  camera: boolean;
  mediaLibrary: boolean;
  audio: boolean;
  storage: boolean;
}

interface PermissionHook {
  permissions: PermissionStatus;
  loading: boolean;
  checkAllPermissions: () => Promise<void>;
  requestPermission: (type: keyof PermissionStatus) => Promise<boolean>;
  openSettings: () => void;
}

export const usePermissions = (): PermissionHook => {
  const [permissions, setPermissions] = useState<PermissionStatus>({
    location: false,
    camera: false,
    mediaLibrary: false,
    audio: false,
    storage: false,
  });
  const [loading, setLoading] = useState(false);

  const checkLocationPermission = async (): Promise<boolean> => {
    try {
      const { status } = await Location.getForegroundPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error checking location permission:', error);
      return false;
    }
  };

  const checkCameraPermission = async (): Promise<boolean> => {
    try {
      const { status } = await ImagePicker.getCameraPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error checking camera permission:', error);
      return false;
    }
  };

  const checkMediaLibraryPermission = async (): Promise<boolean> => {
    try {
      const { status } = await ImagePicker.getMediaLibraryPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error checking media library permission:', error);
      return false;
    }
  };

  const checkAudioPermission = async (): Promise<boolean> => {
    try {
      const { granted } = await AudioModule.getRecordingPermissionsAsync();
      return granted;
    } catch (error) {
      console.error('Error checking audio permission:', error);
      return false;
    }
  };

  const checkStoragePermission = async (): Promise<boolean> => {
    try {
      // On iOS, storage access is generally granted by default
      if (Platform.OS === 'ios') return true;
      
      // For Android, check if we can access the documents directory
      const info = await FileSystem.getInfoAsync(FileSystem.documentDirectory);
      return info.exists;
    } catch (error) {
      console.error('Error checking storage permission:', error);
      return false;
    }
  };

  const checkAllPermissions = async () => {
    setLoading(true);
    try {
      const [location, camera, mediaLibrary, audio, storage] = await Promise.all([
        checkLocationPermission(),
        checkCameraPermission(),
        checkMediaLibraryPermission(),
        checkAudioPermission(),
        checkStoragePermission(),
      ]);

      setPermissions({
        location,
        camera,
        mediaLibrary,
        audio,
        storage,
      });
    } catch (error) {
      console.error('Error checking permissions:', error);
    } finally {
      setLoading(false);
    }
  };

  const requestLocationPermission = async (): Promise<boolean> => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      const granted = status === 'granted';
      setPermissions(prev => ({ ...prev, location: granted }));
      return granted;
    } catch (error) {
      console.error('Error requesting location permission:', error);
      return false;
    }
  };

  const requestCameraPermission = async (): Promise<boolean> => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      const granted = status === 'granted';
      setPermissions(prev => ({ ...prev, camera: granted }));
      return granted;
    } catch (error) {
      console.error('Error requesting camera permission:', error);
      return false;
    }
  };

  const requestMediaLibraryPermission = async (): Promise<boolean> => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      const granted = status === 'granted';
      setPermissions(prev => ({ ...prev, mediaLibrary: granted }));
      return granted;
    } catch (error) {
      console.error('Error requesting media library permission:', error);
      return false;
    }
  };

  const requestAudioPermission = async (): Promise<boolean> => {
    try {
      const { granted } = await AudioModule.requestRecordingPermissionsAsync();
      setPermissions(prev => ({ ...prev, audio: granted }));
      return granted;
    } catch (error) {
      console.error('Error requesting audio permission:', error);
      return false;
    }
  };

  const requestStoragePermission = async (): Promise<boolean> => {
    try {
      // On modern Android and iOS, storage permission is handled automatically
      const granted = await checkStoragePermission();
      setPermissions(prev => ({ ...prev, storage: granted }));
      return granted;
    } catch (error) {
      console.error('Error requesting storage permission:', error);
      return false;
    }
  };

  const requestPermission = async (type: keyof PermissionStatus): Promise<boolean> => {
    switch (type) {
      case 'location':
        return await requestLocationPermission();
      case 'camera':
        return await requestCameraPermission();
      case 'mediaLibrary':
        return await requestMediaLibraryPermission();
      case 'audio':
        return await requestAudioPermission();
      case 'storage':
        return await requestStoragePermission();
      default:
        return false;
    }
  };

  const openSettings = () => {
    Alert.alert(
      'Permissions Required',
      'Some features require permissions that were denied. Please enable them in Settings.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Open Settings', onPress: () => Linking.openSettings() },
      ]
    );
  };

  const showPermissionAlert = (type: keyof PermissionStatus, purpose: string) => {
    const permissionNames = {
      location: 'Location',
      camera: 'Camera',
      mediaLibrary: 'Photo Library',
      audio: 'Microphone',
      storage: 'Storage',
    };

    Alert.alert(
      `${permissionNames[type]} Permission Required`,
      `This app needs ${permissionNames[type].toLowerCase()} access ${purpose}. Please grant permission to continue.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Grant Permission',
          onPress: async () => {
            const granted = await requestPermission(type);
            if (!granted) {
              openSettings();
            }
          },
        },
      ]
    );
  };

  // Check permissions on mount
  useEffect(() => {
    checkAllPermissions();
  }, []);

  return {
    permissions,
    loading,
    checkAllPermissions,
    requestPermission,
    openSettings,
  };
};

// Individual permission request functions with user-friendly alerts
export const requestLocationWithAlert = async (): Promise<boolean> => {
  const { status } = await Location.getForegroundPermissionsAsync();
  
  if (status === 'granted') return true;
  
  return new Promise((resolve) => {
    Alert.alert(
      'Location Permission',
      'This app needs location access to help you find and create location-based badges.',
      [
        { text: 'Not Now', onPress: () => resolve(false), style: 'cancel' },
        {
          text: 'Allow',
          onPress: async () => {
            const { status: newStatus } = await Location.requestForegroundPermissionsAsync();
            resolve(newStatus === 'granted');
          },
        },
      ]
    );
  });
};

export const requestCameraWithAlert = async (): Promise<boolean> => {
  const { status } = await ImagePicker.getCameraPermissionsAsync();
  
  if (status === 'granted') return true;
  
  return new Promise((resolve) => {
    Alert.alert(
      'Camera Permission',
      'This app needs camera access to take photos for your badges.',
      [
        { text: 'Not Now', onPress: () => resolve(false), style: 'cancel' },
        {
          text: 'Allow',
          onPress: async () => {
            const { status: newStatus } = await ImagePicker.requestCameraPermissionsAsync();
            resolve(newStatus === 'granted');
          },
        },
      ]
    );
  });
};

export const requestMediaLibraryWithAlert = async (): Promise<boolean> => {
  const { status } = await ImagePicker.getMediaLibraryPermissionsAsync();
  
  if (status === 'granted') return true;
  
  return new Promise((resolve) => {
    Alert.alert(
      'Photo Library Permission',
      'This app needs photo library access to select images for your badges.',
      [
        { text: 'Not Now', onPress: () => resolve(false), style: 'cancel' },
        {
          text: 'Allow',
          onPress: async () => {
            const { status: newStatus } = await ImagePicker.requestMediaLibraryPermissionsAsync();
            resolve(newStatus === 'granted');
          },
        },
      ]
    );
  });
};

export const requestAudioWithAlert = async (): Promise<boolean> => {
  const { granted } = await AudioModule.getRecordingPermissionsAsync();
  
  if (granted) return true;
  
  return new Promise((resolve) => {
    Alert.alert(
      'Microphone Permission',
      'This app needs microphone access to record audio for your badges.',
      [
        { text: 'Not Now', onPress: () => resolve(false), style: 'cancel' },
        {
          text: 'Allow',
          onPress: async () => {
            const { granted: newGranted } = await AudioModule.requestRecordingPermissionsAsync();
            resolve(newGranted);
          },
        },
      ]
    );
  });
};