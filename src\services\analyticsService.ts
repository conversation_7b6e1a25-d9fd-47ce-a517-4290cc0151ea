// Analytics service - temporarily disabled for compatibility
// TODO: Replace with proper analytics implementation (Firebase Analytics v9+)

export const trackEvent = async (eventName: string, parameters: Record<string, any> = {}) => {
  // No-op implementation for now
  if (__DEV__) {
    console.log(`[Analytics] ${eventName}:`, parameters);
  }
};

export const trackQuestTap = async (questId: string, questTitle: string, userId?: string) => {
  await trackEvent('quest_tap', {
    quest_id: questId,
    quest_title: questTitle,
    user_id: userId,
    timestamp: new Date().toISOString(),
  });
};

export const trackQuestStart = async (questId: string, questTitle: string, userId?: string) => {
  await trackEvent('quest_start', {
    quest_id: questId,
    quest_title: questTitle,
    user_id: userId,
    timestamp: new Date().toISOString(),
  });
};

export const trackQuestComplete = async (questId: string, questTitle: string, userId?: string, completionTime?: number) => {
  await trackEvent('quest_complete', {
    quest_id: questId,
    quest_title: questTitle,
    user_id: userId,
    completion_time: completionTime,
    timestamp: new Date().toISOString(),
  });
};

export const trackBadgeEarned = async (badgeId: string, badgeName: string, userId?: string) => {
  await trackEvent('badge_earned', {
    badge_id: badgeId,
    badge_name: badgeName,
    user_id: userId,
    timestamp: new Date().toISOString(),
  });
};

export const trackUserRegistration = async (userId: string, method: 'email' | 'social') => {
  await trackEvent('user_registration', {
    user_id: userId,
    registration_method: method,
    timestamp: new Date().toISOString(),
  });
};

export const trackUserLogin = async (userId: string, method: 'email' | 'social') => {
  await trackEvent('user_login', {
    user_id: userId,
    login_method: method,
    timestamp: new Date().toISOString(),
  });
};

export const trackScreenView = async (screenName: string, userId?: string) => {
  await trackEvent('screen_view', {
    screen_name: screenName,
    user_id: userId,
    timestamp: new Date().toISOString(),
  });
};

export const trackMapView = async (userId?: string) => {
  await trackEvent('map_view', {
    user_id: userId,
    timestamp: new Date().toISOString(),
  });
};

export const trackProfileShare = async (userId?: string) => {
  await trackEvent('profile_share', {
    user_id: userId,
    timestamp: new Date().toISOString(),
  });
};

export const trackAchievementView = async (achievementType: string, userId?: string) => {
  await trackEvent('achievement_view', {
    achievement_type: achievementType,
    user_id: userId,
    timestamp: new Date().toISOString(),
  });
};