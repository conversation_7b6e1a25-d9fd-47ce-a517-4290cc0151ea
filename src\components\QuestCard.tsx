import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ImageBackground } from 'react-native';
import { Card } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { lightTheme, questDifficulty, createGlowEffect } from '../config/theme';

interface QuestCardProps {
  id: string;
  title: string;
  description: string;
  coverImage: string;
  progress: number;
  totalSteps: number;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  category: string;
  onPress: () => void;
}

export default function QuestCard({ 
  id, 
  title, 
  description, 
  coverImage, 
  progress, 
  totalSteps, 
  difficulty, 
  category,
  onPress 
}: QuestCardProps) {
  const progressPercentage = (progress / totalSteps) * 100;
  
  const getDifficultyConfig = () => {
    switch (difficulty) {
      case 'Easy': return questDifficulty.easy;
      case 'Medium': return questDifficulty.medium;
      case 'Hard': return questDifficulty.hard;
      default: return questDifficulty.easy;
    }
  };

  const difficultyConfig = getDifficultyConfig();

  const getCompletionMessage = () => {
    if (progress === totalSteps) {
      return { text: '✅ Completed!', color: lightTheme.colors.success };
    }
    if (progress === 0) {
      return { text: '🎯 Start exploring!', color: lightTheme.colors.primaryAccent };
    }
    return { text: `🔥 ${totalSteps - progress} steps left`, color: lightTheme.colors.warning };
  };

  const completionMessage = getCompletionMessage();

  const getBadgeCount = () => {
    if (progress === totalSteps) return 3;
    if (progress >= totalSteps * 0.7) return 2;
    if (progress >= totalSteps * 0.3) return 1;
    return 0;
  };

  const getCardGlow = () => {
    if (progress === totalSteps) {
      return createGlowEffect(lightTheme.colors.success, 0.4);
    }
    if (progress >= totalSteps * 0.7) {
      return createGlowEffect(lightTheme.colors.primaryAccent, 0.3);
    }
    return {};
  };

  return (
    <TouchableOpacity onPress={onPress} style={styles.cardContainer}>
      <Card style={[styles.card, getCardGlow()]}>
        <ImageBackground
          source={{ uri: coverImage }}
          style={styles.coverImage}
          imageStyle={styles.coverImageStyle}
        >
          <View style={styles.overlay} />
          <View style={styles.badges}>
            <LinearGradient
              colors={difficultyConfig.gradient as any}
              style={styles.difficultyBadge}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <Text style={styles.difficultyText}>{difficultyConfig.icon} {difficulty}</Text>
            </LinearGradient>
            <View style={styles.categoryBadge}>
              <Text style={styles.categoryText}>{category}</Text>
            </View>
          </View>
          
          <View style={styles.badgeIndicator}>
            {[...Array(3)].map((_, index) => (
              <View 
                key={index} 
                style={[
                  styles.badgeDot,
                  { 
                    backgroundColor: index < getBadgeCount() ? lightTheme.colors.secondaryAccent : 'rgba(255,255,255,0.3)',
                    transform: [{ scale: index < getBadgeCount() ? 1 : 0.7 }]
                  }
                ]} 
              />
            ))}
          </View>
        </ImageBackground>
        
        <LinearGradient
          colors={[lightTheme.colors.surface, lightTheme.colors.surfaceElevated] as any}
          style={styles.content}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
        >
          <Text style={styles.title}>{title}</Text>
          <Text style={styles.description}>{description}</Text>
          
          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <LinearGradient
                colors={lightTheme.gradients.primary as any}
                style={[
                  styles.progressFill, 
                  { width: `${progressPercentage}%` }
                ]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              />
            </View>
            <Text style={styles.progressText}>
              {progress}/{totalSteps} steps
            </Text>
          </View>
          
          <View style={styles.footer}>
            <Text style={[styles.completionMessage, { color: completionMessage.color }]}>
              {completionMessage.text}
            </Text>
            {progress < totalSteps && (
              <Text style={styles.fomoText}>
                🏆 Others are racing ahead!
              </Text>
            )}
          </View>
        </LinearGradient>
      </Card>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  cardContainer: {
    marginRight: lightTheme.spacing.md,
  },
  card: {
    width: 300,
    borderRadius: lightTheme.borderRadius.xxl,
    overflow: 'hidden',
    ...lightTheme.shadows.large,
    backgroundColor: 'transparent',
  },
  coverImage: {
    height: 180,
    justifyContent: 'space-between',
    padding: lightTheme.spacing.lg,
  },
  coverImageStyle: {
    borderTopLeftRadius: lightTheme.borderRadius.xxl,
    borderTopRightRadius: lightTheme.borderRadius.xxl,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    borderTopLeftRadius: lightTheme.borderRadius.xxl,
    borderTopRightRadius: lightTheme.borderRadius.xxl,
  },
  badges: {
    flexDirection: 'row',
    gap: lightTheme.spacing.sm,
  },
  difficultyBadge: {
    paddingHorizontal: lightTheme.spacing.md,
    paddingVertical: lightTheme.spacing.sm,
    borderRadius: lightTheme.borderRadius.lg,
    ...lightTheme.shadows.small,
  },
  difficultyText: {
    ...lightTheme.typography.buttonSmall,
    color: lightTheme.colors.white,
    fontWeight: '700',
  },
  categoryBadge: {
    paddingHorizontal: lightTheme.spacing.md,
    paddingVertical: lightTheme.spacing.sm,
    borderRadius: lightTheme.borderRadius.lg,
    backgroundColor: 'rgba(255, 255, 255, 0.25)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  categoryText: {
    ...lightTheme.typography.buttonSmall,
    color: lightTheme.colors.white,
    fontWeight: '600',
  },
  badgeIndicator: {
    flexDirection: 'row',
    gap: lightTheme.spacing.xs,
    alignSelf: 'flex-end',
  },
  badgeDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    ...lightTheme.shadows.small,
  },
  content: {
    padding: lightTheme.spacing.lg,
  },
  title: {
    ...lightTheme.typography.h5,
    color: lightTheme.colors.textPrimary,
    marginBottom: lightTheme.spacing.sm,
  },
  description: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textSecondary,
    marginBottom: lightTheme.spacing.lg,
    lineHeight: 20,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: lightTheme.spacing.sm,
  },
  progressBar: {
    flex: 1,
    height: 8,
    backgroundColor: lightTheme.colors.borderLight,
    borderRadius: 4,
    marginRight: lightTheme.spacing.md,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
    ...lightTheme.shadows.small,
  },
  progressText: {
    ...lightTheme.typography.buttonSmall,
    color: lightTheme.colors.textSecondary,
    fontWeight: '600',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: lightTheme.spacing.sm,
  },
  completionMessage: {
    ...lightTheme.typography.buttonSmall,
    fontWeight: '700',
  },
  fomoText: {
    ...lightTheme.typography.caption,
    color: lightTheme.colors.secondaryAccent,
    fontWeight: '600',
    fontStyle: 'italic',
  },
});