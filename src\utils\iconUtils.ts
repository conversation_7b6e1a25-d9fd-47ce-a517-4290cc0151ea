// Valid Ionicons that actually exist
export const VALID_IONICONS = [
  'star', 'star-outline', 'heart', 'heart-outline', 'trophy', 'trophy-outline',
  'medal', 'medal-outline', 'ribbon', 'ribbon-outline', 'flag', 'flag-outline',
  'location', 'location-outline', 'map', 'map-outline', 'compass', 'compass-outline',
  'camera', 'camera-outline', 'image', 'image-outline', 'videocam', 'videocam-outline',
  'musical-notes', 'musical-notes-outline', 'headset', 'headset-outline',
  'book', 'book-outline', 'library', 'library-outline', 'school', 'school-outline',
  'bicycle', 'bicycle-outline', 'car', 'car-outline', 'airplane', 'airplane-outline',
  'boat', 'boat-outline', 'train', 'train-outline', 'walk', 'walk-outline',
  'fitness', 'fitness-outline', 'barbell', 'barbell-outline', 'basketball', 'basketball-outline',
  'football', 'football-outline', 'tennisball', 'tennisball-outline',
  'leaf', 'leaf-outline', 'flower', 'flower-outline', 'sunny', 'sunny-outline',
  'moon', 'moon-outline', 'cloudy', 'cloudy-outline', 'rainy', 'rainy-outline',
  'snow', 'snow-outline', 'thunderstorm', 'thunderstorm-outline',
  'restaurant', 'restaurant-outline', 'cafe', 'cafe-outline', 'wine', 'wine-outline',
  'pizza', 'pizza-outline', 'ice-cream', 'ice-cream-outline',
  'home', 'home-outline', 'business', 'business-outline', 'storefront', 'storefront-outline',
  'earth', 'earth-outline', 'globe', 'globe-outline', 'telescope', 'telescope-outline',
  'rocket', 'rocket-outline', 'planet', 'planet-outline'
];

// Check if a string is a valid Ionicon name
export const isValidIonicon = (iconName: string): boolean => {
  return VALID_IONICONS.includes(iconName);
};

// Check if a string is an emoji (basic check for Unicode emoji characters)
export const isEmoji = (str: string): boolean => {
  // Basic emoji regex pattern - matches most Unicode emoji
  const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u;
  return emojiRegex.test(str);
};

// Get the icon type
export const getIconType = (icon: string): 'ionicon' | 'emoji' | 'unknown' => {
  if (isValidIonicon(icon)) {
    return 'ionicon';
  }
  if (isEmoji(icon)) {
    return 'emoji';
  }
  return 'unknown';
};