import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  Image,
  FlatList,
} from 'react-native';
import { 
  Button, 
  Card, 
  IconButton,
  Portal,
  Modal,
  List,
  Searchbar,
} from 'react-native-paper';
import { useFormContext, Controller } from 'react-hook-form';
import * as ImagePicker from 'expo-image-picker';
import { lightTheme } from '../../../config/theme';
import { BadgeFormData } from '../../../services/BadgeService';
import { Ionicons } from '@expo/vector-icons';

// Material Icons for badges
const BADGE_ICONS = [
  { name: 'location-pin', icon: 'location-pin' },
  { name: 'trophy', icon: 'trophy' },
  { name: 'star', icon: 'star' },
  { name: 'temple-hindu', icon: 'temple-hindu' },
  { name: 'nature', icon: 'nature' },
  { name: 'location-city', icon: 'location-city' },
  { name: 'account-balance', icon: 'account-balance' },
  { name: 'palette', icon: 'palette' },
  { name: 'restaurant', icon: 'restaurant' },
  { name: 'shopping-bag', icon: 'shopping-bag' },
  { name: 'movie', icon: 'movie' },
  { name: 'sports-soccer', icon: 'sports-soccer' },
  { name: 'school', icon: 'school' },
  { name: 'local-hospital', icon: 'local-hospital' },
  { name: 'directions-bus', icon: 'directions-bus' },
  { name: 'local-gas-station', icon: 'local-gas-station' },
  { name: 'local-library', icon: 'local-library' },
  { name: 'local-cafe', icon: 'local-cafe' },
  { name: 'local-pharmacy', icon: 'local-pharmacy' },
  { name: 'local-post-office', icon: 'local-post-office' },
  { name: 'local-police', icon: 'local-police' },
  { name: 'local-fire-department', icon: 'local-fire-department' },
  { name: 'park', icon: 'park' },
  { name: 'beach-access', icon: 'beach-access' },
  { name: 'terrain', icon: 'terrain' },
  { name: 'waterfall-chart', icon: 'waterfall-chart' },
  { name: 'landscape', icon: 'landscape' },
  { name: 'festival', icon: 'festival' },
  { name: 'museum', icon: 'museum' },
  { name: 'castle', icon: 'castle' },
  { name: 'church', icon: 'church' },
  { name: 'synagogue', icon: 'synagogue' },
  { name: 'mosque', icon: 'mosque' },
  { name: 'explore', icon: 'explore' },
  { name: 'compass', icon: 'compass' },
  { name: 'map', icon: 'map' },
  { name: 'place', icon: 'place' },
  { name: 'room', icon: 'room' },
  { name: 'my-location', icon: 'my-location' },
  { name: 'navigation', icon: 'navigation' },
  { name: 'near-me', icon: 'near-me' },
  { name: 'directions', icon: 'directions' },
  { name: 'timeline', icon: 'timeline' },
  { name: 'history', icon: 'history' },
  { name: 'schedule', icon: 'schedule' },
  { name: 'today', icon: 'today' },
  { name: 'event', icon: 'event' },
  { name: 'celebration', icon: 'celebration' },
  { name: 'cake', icon: 'cake' },
  { name: 'local-florist', icon: 'local-florist' },
  { name: 'spa', icon: 'spa' },
  { name: 'fitness-center', icon: 'fitness-center' },
  { name: 'pool', icon: 'pool' },
  { name: 'golf-course', icon: 'golf-course' },
  { name: 'skateboarding', icon: 'skateboarding' },
  { name: 'snowboarding', icon: 'snowboarding' },
  { name: 'surfing', icon: 'surfing' },
  { name: 'sailing', icon: 'sailing' },
  { name: 'rowing', icon: 'rowing' },
  { name: 'kayaking', icon: 'kayaking' },
  { name: 'kitesurfing', icon: 'kitesurfing' },
  { name: 'paragliding', icon: 'paragliding' },
  { name: 'hang-gliding', icon: 'hang-gliding' },
  { name: 'rock-climbing', icon: 'rock-climbing' },
  { name: 'hiking', icon: 'hiking' },
  { name: 'camping', icon: 'camping' },
  { name: 'cabin', icon: 'cabin' },
  { name: 'rv-hookup', icon: 'rv-hookup' },
  { name: 'fireplace', icon: 'fireplace' },
  { name: 'outdoor-grill', icon: 'outdoor-grill' },
  { name: 'night-shelter', icon: 'night-shelter' },
  { name: 'roofing', icon: 'roofing' },
  { name: 'foundation', icon: 'foundation' },
  { name: 'architecture', icon: 'architecture' },
  { name: 'engineering', icon: 'engineering' },
  { name: 'construction', icon: 'construction' },
  { name: 'handyman', icon: 'handyman' },
  { name: 'plumbing', icon: 'plumbing' },
  { name: 'electrical-services', icon: 'electrical-services' },
  { name: 'hvac', icon: 'hvac' },
  { name: 'carpenter', icon: 'carpenter' },
  { name: 'format-paint', icon: 'format-paint' },
  { name: 'hardware', icon: 'hardware' },
  { name: 'build', icon: 'build' },
  { name: 'build-circle', icon: 'build-circle' },
  { name: 'settings', icon: 'settings' },
  { name: 'tune', icon: 'tune' },
  { name: 'precision-manufacturing', icon: 'precision-manufacturing' },
  { name: 'factory', icon: 'factory' },
  { name: 'warehouse', icon: 'warehouse' },
  { name: 'inventory', icon: 'inventory' },
  { name: 'storefront', icon: 'storefront' },
  { name: 'store', icon: 'store' },
  { name: 'local-mall', icon: 'local-mall' },
  { name: 'local-grocery-store', icon: 'local-grocery-store' },
  { name: 'local-convenience-store', icon: 'local-convenience-store' },
  { name: 'local-atm', icon: 'local-atm' },
  { name: 'account-balance-wallet', icon: 'account-balance-wallet' },
  { name: 'credit-card', icon: 'credit-card' },
  { name: 'payment', icon: 'payment' },
  { name: 'monetization-on', icon: 'monetization-on' },
  { name: 'currency-rupee', icon: 'currency-rupee' },
  { name: 'savings', icon: 'savings' },
  { name: 'request-quote', icon: 'request-quote' },
  { name: 'receipt', icon: 'receipt' },
  { name: 'point-of-sale', icon: 'point-of-sale' },
  { name: 'shopping-cart', icon: 'shopping-cart' },
  { name: 'add-shopping-cart', icon: 'add-shopping-cart' },
  { name: 'remove-shopping-cart', icon: 'remove-shopping-cart' },
];

interface IconTabProps {
  selectedImage: any;
  setSelectedImage: (image: any) => void;
}

export default function BadgeIconTab({ selectedImage, setSelectedImage }: IconTabProps) {
  const { control, watch, setValue, formState: { errors } } = useFormContext<BadgeFormData>();
  const [iconModalVisible, setIconModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const selectedIcon = watch('icon');

  const filteredIcons = BADGE_ICONS.filter(icon =>
    icon.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const requestPermission = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission required', 'Please grant permission to access your photo library');
      return false;
    }
    return true;
  };

  const pickImage = async () => {
    const hasPermission = await requestPermission();
    if (!hasPermission) return;

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaType.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      setSelectedImage(result.assets[0]);
    }
  };

  const takePicture = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission required', 'Please grant permission to access your camera');
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      setSelectedImage(result.assets[0]);
    }
  };

  const removeImage = () => {
    setSelectedImage(null);
  };

  const renderIconItem = ({ item }: { item: typeof BADGE_ICONS[0] }) => (
    <Button
      mode={selectedIcon === item.icon ? 'contained' : 'outlined'}
      onPress={() => {
        setValue('icon', item.icon);
        setIconModalVisible(false);
      }}
      style={[
        styles.iconButton,
        selectedIcon === item.icon && styles.selectedIconButton
      ]}
      contentStyle={styles.iconButtonContent}
    >
      <Ionicons 
        name={item.icon as any} 
        size={24} 
        color={selectedIcon === item.icon ? lightTheme.colors.white : lightTheme.colors.textPrimary} 
      />
    </Button>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.sectionTitle}>🎨 Badge Icon</Text>
          <Text style={styles.sectionSubtitle}>
            Choose a predefined icon or upload a custom image
          </Text>
          
          {/* Selected Icon Display */}
          <View style={styles.selectedIconContainer}>
            <Text style={styles.inputLabel}>Selected Icon:</Text>
            <View style={styles.iconPreview}>
              <View style={styles.iconPreviewBackground}>
                <Ionicons 
                  name={selectedIcon as any} 
                  size={48} 
                  color={lightTheme.colors.primaryAccent} 
                />
              </View>
              <Button
                mode="outlined"
                onPress={() => setIconModalVisible(true)}
                style={styles.changeIconButton}
              >
                Change Icon
              </Button>
            </View>
            {errors.icon && (
              <Text style={styles.errorText}>{errors.icon.message}</Text>
            )}
          </View>

          {/* Quick Icon Selection */}
          <View style={styles.quickIconsContainer}>
            <Text style={styles.inputLabel}>Quick Selection:</Text>
            <View style={styles.quickIconsGrid}>
              {BADGE_ICONS.slice(0, 12).map((icon) => (
                <Button
                  key={icon.name}
                  mode={selectedIcon === icon.icon ? 'contained' : 'outlined'}
                  onPress={() => setValue('icon', icon.icon)}
                  style={[
                    styles.quickIconButton,
                    selectedIcon === icon.icon && styles.selectedQuickIconButton
                  ]}
                  contentStyle={styles.quickIconButtonContent}
                >
                  <Ionicons 
                    name={icon.icon as any} 
                    size={20} 
                    color={selectedIcon === icon.icon ? lightTheme.colors.white : lightTheme.colors.textPrimary} 
                  />
                </Button>
              ))}
            </View>
          </View>
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.sectionTitle}>🖼️ Custom Image (Optional)</Text>
          <Text style={styles.sectionSubtitle}>
            Upload a custom image to use as the badge icon
          </Text>
          
          {selectedImage ? (
            <View style={styles.imagePreview}>
              <Image source={{ uri: selectedImage.uri }} style={styles.previewImage} />
              <View style={styles.imageActions}>
                <Button
                  mode="outlined"
                  onPress={pickImage}
                  style={styles.imageButton}
                >
                  Change Image
                </Button>
                <Button
                  mode="outlined"
                  onPress={removeImage}
                  style={styles.imageButton}
                  textColor={lightTheme.colors.error}
                >
                  Remove
                </Button>
              </View>
            </View>
          ) : (
            <View style={styles.imageUpload}>
              <View style={styles.uploadPlaceholder}>
                <Ionicons name="image-outline" size={48} color={lightTheme.colors.textSecondary} />
                <Text style={styles.uploadText}>No custom image selected</Text>
              </View>
              <View style={styles.uploadActions}>
                <Button
                  mode="contained"
                  onPress={pickImage}
                  style={styles.uploadButton}
                  icon="folder-open"
                >
                  Choose from Library
                </Button>
                <Button
                  mode="outlined"
                  onPress={takePicture}
                  style={styles.uploadButton}
                  icon="camera"
                >
                  Take Photo
                </Button>
              </View>
            </View>
          )}
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.sectionTitle}>ℹ️ Guidelines</Text>
          <View style={styles.guidelinesContainer}>
            <Text style={styles.guidelineText}>• Icons should be simple and recognizable</Text>
            <Text style={styles.guidelineText}>• Custom images should be square (1:1 aspect ratio)</Text>
            <Text style={styles.guidelineText}>• High contrast images work best</Text>
            <Text style={styles.guidelineText}>• Avoid text in icons as they may be hard to read</Text>
            <Text style={styles.guidelineText}>• File size should be under 5MB</Text>
          </View>
        </Card.Content>
      </Card>

      {/* Icon Selection Modal */}
      <Portal>
        <Modal
          visible={iconModalVisible}
          onDismiss={() => setIconModalVisible(false)}
          contentContainerStyle={styles.modalContainer}
        >
          <Card style={styles.modalCard}>
            <Card.Title title="Select Icon" />
            <Card.Content>
              <Searchbar
                placeholder="Search icons..."
                onChangeText={setSearchQuery}
                value={searchQuery}
                style={styles.searchBar}
              />
              <FlatList
                data={filteredIcons}
                renderItem={renderIconItem}
                keyExtractor={(item) => item.name}
                numColumns={4}
                style={styles.iconGrid}
                showsVerticalScrollIndicator={false}
              />
            </Card.Content>
          </Card>
        </Modal>
      </Portal>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  card: {
    marginBottom: lightTheme.spacing.lg,
    borderRadius: lightTheme.borderRadius.lg,
  },
  sectionTitle: {
    ...lightTheme.typography.h6,
    color: lightTheme.colors.textPrimary,
    marginBottom: lightTheme.spacing.sm,
  },
  sectionSubtitle: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textSecondary,
    marginBottom: lightTheme.spacing.lg,
  },
  inputLabel: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textPrimary,
    marginBottom: lightTheme.spacing.sm,
    fontWeight: '600',
  },
  selectedIconContainer: {
    marginBottom: lightTheme.spacing.lg,
  },
  iconPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: lightTheme.spacing.lg,
  },
  iconPreviewBackground: {
    backgroundColor: lightTheme.colors.primaryAccent + '20',
    padding: lightTheme.spacing.lg,
    borderRadius: lightTheme.borderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
  },
  changeIconButton: {
    flex: 1,
  },
  quickIconsContainer: {
    marginBottom: lightTheme.spacing.lg,
  },
  quickIconsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: lightTheme.spacing.sm,
  },
  quickIconButton: {
    width: 50,
    height: 50,
    borderRadius: lightTheme.borderRadius.md,
  },
  selectedQuickIconButton: {
    backgroundColor: lightTheme.colors.primaryAccent,
  },
  quickIconButtonContent: {
    width: 50,
    height: 50,
  },
  imagePreview: {
    alignItems: 'center',
    gap: lightTheme.spacing.lg,
  },
  previewImage: {
    width: 150,
    height: 150,
    borderRadius: lightTheme.borderRadius.lg,
    backgroundColor: lightTheme.colors.surface,
  },
  imageActions: {
    flexDirection: 'row',
    gap: lightTheme.spacing.md,
  },
  imageButton: {
    minWidth: 120,
  },
  imageUpload: {
    alignItems: 'center',
    gap: lightTheme.spacing.lg,
  },
  uploadPlaceholder: {
    alignItems: 'center',
    padding: lightTheme.spacing.xl,
    borderWidth: 2,
    borderStyle: 'dashed',
    borderColor: lightTheme.colors.borderLight,
    borderRadius: lightTheme.borderRadius.lg,
    backgroundColor: lightTheme.colors.surface,
  },
  uploadText: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textSecondary,
    marginTop: lightTheme.spacing.sm,
  },
  uploadActions: {
    flexDirection: 'row',
    gap: lightTheme.spacing.md,
  },
  uploadButton: {
    minWidth: 140,
  },
  guidelinesContainer: {
    gap: lightTheme.spacing.sm,
  },
  guidelineText: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textSecondary,
  },
  modalContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalCard: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: lightTheme.borderRadius.lg,
  },
  searchBar: {
    marginBottom: lightTheme.spacing.md,
  },
  iconGrid: {
    maxHeight: 400,
  },
  iconButton: {
    width: 60,
    height: 60,
    margin: lightTheme.spacing.xs,
    borderRadius: lightTheme.borderRadius.md,
  },
  selectedIconButton: {
    backgroundColor: lightTheme.colors.primaryAccent,
  },
  iconButtonContent: {
    width: 60,
    height: 60,
  },
  errorText: {
    ...lightTheme.typography.caption,
    color: lightTheme.colors.error,
    marginTop: lightTheme.spacing.sm,
  },
});