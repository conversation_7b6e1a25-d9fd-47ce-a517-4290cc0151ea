import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Image,
  Animated,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../App';
import { lightTheme } from '../config/theme';
import { sendEmailVerification } from 'firebase/auth';
import { auth } from '../config/firebase';

type RegisterProfileScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'RegisterProfile'>;

export default function RegisterProfileScreen() {
  const [displayName, setDisplayName] = useState('');
  const [password, setPassword] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showCelebration, setShowCelebration] = useState(false);
  const [step, setStep] = useState<'verify' | 'profile'>('verify');
  
  const navigation = useNavigation<RegisterProfileScreenNavigationProp>();
  const route = useRoute();
  const { email, userId } = route.params as { email: string; userId: string };

  const celebrationAnimation = new Animated.Value(0);

  const handleVerifyCode = async () => {
    if (!verificationCode.trim()) {
      Alert.alert('Code Required', 'Please enter the verification code.');
      return;
    }

    // For now, simulate email verification
    // In a real app, you would check if the user's email is verified
    const user = auth.currentUser;
    if (user) {
      await user.reload();
      if (user.emailVerified) {
        setStep('profile');
      } else {
        Alert.alert('Email Not Verified', 'Please check your email and click the verification link before proceeding.');
      }
    } else {
      Alert.alert('Error', 'Please try registering again.');
    }
  };

  const handleImagePicker = () => {
    Alert.alert(
      'Profile Photo',
      'Choose an option',
      [
        { text: 'Camera', onPress: () => console.log('Camera selected') },
        { text: 'Gallery', onPress: () => console.log('Gallery selected') },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const startCelebrationAnimation = () => {
    setShowCelebration(true);
    Animated.sequence([
      Animated.timing(celebrationAnimation, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(celebrationAnimation, {
        toValue: 0,
        duration: 400,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setShowCelebration(false);
      navigation.navigate('Home');
    });
  };

  const handleFinishSetup = async () => {
    if (!displayName.trim()) {
      Alert.alert('Display Name Required', 'Please enter a display name.');
      return;
    }

    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      startCelebrationAnimation();
    }, 1000);
  };

  const handleResendCode = async () => {
    try {
      const user = auth.currentUser;
      if (user) {
        await sendEmailVerification(user);
        Alert.alert('Verification Email Sent', 'A new verification email has been sent to your email address.');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to send verification email. Please try again.');
    }
  };

  if (step === 'verify') {
    return (
      <SafeAreaView style={styles.container}>
        <KeyboardAvoidingView
          style={styles.keyboardContainer}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <View style={styles.header}>
            <Text style={styles.title}>Check Your Email</Text>
            <Text style={styles.subtitle}>
              We've sent a verification code to{'\n'}{email}
            </Text>
          </View>

          <View style={styles.form}>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Verification Code</Text>
              <TextInput
                style={styles.input}
                placeholder="Enter 6-digit code"
                placeholderTextColor={lightTheme.colors.textSecondary}
                value={verificationCode}
                onChangeText={setVerificationCode}
                keyboardType="number-pad"
                maxLength={6}
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            <TouchableOpacity
              style={styles.continueButton}
              onPress={handleVerifyCode}
            >
              <Text style={styles.continueButtonText}>Verify Code</Text>
            </TouchableOpacity>

            <TouchableOpacity onPress={handleResendCode} style={styles.resendContainer}>
              <Text style={styles.resendText}>
                Didn't receive the code? <Text style={styles.resendLink}>Resend</Text>
              </Text>
            </TouchableOpacity>
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.header}>
          <Text style={styles.title}>Complete Your Profile</Text>
          <Text style={styles.subtitle}>
            Just a few more details to get you started
          </Text>
        </View>

        <View style={styles.form}>
          <View style={styles.avatarContainer}>
            <TouchableOpacity style={styles.avatarButton} onPress={handleImagePicker}>
              {profileImage ? (
                <Image source={{ uri: profileImage }} style={styles.avatar} />
              ) : (
                <View style={styles.avatarPlaceholder}>
                  <Text style={styles.avatarPlaceholderText}>📸</Text>
                </View>
              )}
            </TouchableOpacity>
            <Text style={styles.avatarLabel}>Add Profile Photo</Text>
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Display Name</Text>
            <TextInput
              style={styles.input}
              placeholder="How should others see you?"
              placeholderTextColor={lightTheme.colors.textSecondary}
              value={displayName}
              onChangeText={setDisplayName}
              autoCapitalize="words"
              autoCorrect={false}
              editable={!isLoading}
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Password (Optional)</Text>
            <TextInput
              style={styles.input}
              placeholder="Create a password for easy sign-in"
              placeholderTextColor={lightTheme.colors.textSecondary}
              value={password}
              onChangeText={setPassword}
              secureTextEntry
              autoCapitalize="none"
              autoCorrect={false}
              editable={!isLoading}
            />
          </View>

          <TouchableOpacity
            style={[styles.finishButton, isLoading && styles.buttonDisabled]}
            onPress={handleFinishSetup}
            disabled={isLoading}
          >
            <Text style={styles.finishButtonText}>
              {isLoading ? 'Setting Up...' : 'Finish Setup'}
            </Text>
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>

      {showCelebration && (
        <Animated.View
          style={[
            styles.celebrationOverlay,
            {
              opacity: celebrationAnimation,
              transform: [
                {
                  scale: celebrationAnimation.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0.8, 1.2],
                  }),
                },
              ],
            },
          ]}
        >
          <Text style={styles.celebrationBadge}>🎉</Text>
          <Text style={styles.celebrationText}>Welcome to MyCityQuest!</Text>
        </Animated.View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: lightTheme.colors.background,
  },
  keyboardContainer: {
    flex: 1,
    paddingHorizontal: lightTheme.spacing.lg,
  },
  header: {
    alignItems: 'center',
    marginTop: lightTheme.spacing.xxl,
    marginBottom: lightTheme.spacing.xl,
  },
  title: {
    ...lightTheme.typography.h1,
    color: lightTheme.colors.textPrimary,
    textAlign: 'center',
    marginBottom: lightTheme.spacing.sm,
  },
  subtitle: {
    ...lightTheme.typography.body1,
    color: lightTheme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  form: {
    flex: 1,
    justifyContent: 'center',
  },
  avatarContainer: {
    alignItems: 'center',
    marginBottom: lightTheme.spacing.xl,
  },
  avatarButton: {
    marginBottom: lightTheme.spacing.sm,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  avatarPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: lightTheme.colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: lightTheme.colors.primaryAccent,
    borderStyle: 'dashed',
  },
  avatarPlaceholderText: {
    fontSize: 32,
  },
  avatarLabel: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textSecondary,
  },
  inputContainer: {
    marginBottom: lightTheme.spacing.lg,
  },
  inputLabel: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textPrimary,
    marginBottom: lightTheme.spacing.sm,
    fontWeight: '600',
  },
  input: {
    backgroundColor: lightTheme.colors.surface,
    borderRadius: lightTheme.borderRadius.lg,
    paddingHorizontal: lightTheme.spacing.md,
    paddingVertical: lightTheme.spacing.md,
    fontSize: 16,
    color: lightTheme.colors.textPrimary,
    borderWidth: 2,
    borderColor: 'transparent',
    ...lightTheme.shadows.small,
  },
  continueButton: {
    backgroundColor: lightTheme.colors.primaryAccent,
    borderRadius: lightTheme.borderRadius.lg,
    paddingVertical: lightTheme.spacing.md,
    alignItems: 'center',
    marginBottom: lightTheme.spacing.lg,
    ...lightTheme.shadows.medium,
  },
  continueButtonText: {
    ...lightTheme.typography.button,
    color: lightTheme.colors.white,
  },
  finishButton: {
    backgroundColor: lightTheme.colors.secondaryAccent,
    borderRadius: lightTheme.borderRadius.lg,
    paddingVertical: lightTheme.spacing.md,
    alignItems: 'center',
    marginTop: lightTheme.spacing.md,
    ...lightTheme.shadows.medium,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  finishButtonText: {
    ...lightTheme.typography.button,
    color: lightTheme.colors.white,
  },
  resendContainer: {
    alignItems: 'center',
    marginTop: lightTheme.spacing.md,
  },
  resendText: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textSecondary,
  },
  resendLink: {
    color: lightTheme.colors.primaryAccent,
    fontWeight: '600',
  },
  celebrationOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  celebrationBadge: {
    fontSize: 80,
    marginBottom: lightTheme.spacing.lg,
  },
  celebrationText: {
    ...lightTheme.typography.h2,
    color: lightTheme.colors.white,
    textAlign: 'center',
  },
});