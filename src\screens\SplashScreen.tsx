import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  Animated,
  ImageBackground,
} from 'react-native';

const { width, height } = Dimensions.get('window');

interface SplashScreenProps {
  onFinish: () => void;
}

export default function SplashScreen({ onFinish }: SplashScreenProps) {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();

    const timer = setTimeout(() => {
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }).start(() => {
        onFinish();
      });
    }, 2500);

    return () => clearTimeout(timer);
  }, [fadeAnim, scaleAnim, onFinish]);

  return (
    <View style={styles.container}>
      <ImageBackground
        source={{ uri: 'https://images.unsplash.com/photo-1477959858617-67f85cf4f1df?w=800&h=600&fit=crop' }}
        style={styles.backgroundImage}
        resizeMode="cover">
        <View style={styles.overlay} />
        
        <Animated.View
          style={[
            styles.content,
            {
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
            },
          ]}>
          
          <View style={styles.collageContainer}>
            <View style={styles.collageGrid}>
              <View style={[styles.collageItem, styles.largeItem]}>
                <ImageBackground
                  source={{ uri: 'https://images.unsplash.com/photo-1513635269975-59663e0ac1ad?w=400&h=300&fit=crop' }}
                  style={styles.collageImage}
                  resizeMode="cover"
                />
              </View>
              <View style={[styles.collageItem, styles.mediumItem]}>
                <ImageBackground
                  source={{ uri: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73073?w=300&h=200&fit=crop' }}
                  style={styles.collageImage}
                  resizeMode="cover"
                />
              </View>
              <View style={[styles.collageItem, styles.smallItem]}>
                <ImageBackground
                  source={{ uri: 'https://images.unsplash.com/photo-1467269204594-9661b134dd2b?w=200&h=150&fit=crop' }}
                  style={styles.collageImage}
                  resizeMode="cover"
                />
              </View>
              <View style={[styles.collageItem, styles.mediumItem]}>
                <ImageBackground
                  source={{ uri: 'https://images.unsplash.com/photo-1520637836862-4d197d17c60a?w=300&h=200&fit=crop' }}
                  style={styles.collageImage}
                  resizeMode="cover"
                />
              </View>
            </View>
          </View>

          <View style={styles.taglineContainer}>
            <Text style={styles.appName}>MyCityQuest</Text>
            <Text style={styles.tagline}>Turn your world into a treasure hunt</Text>
          </View>
        </Animated.View>
      </ImageBackground>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  collageContainer: {
    marginBottom: 60,
  },
  collageGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    alignItems: 'center',
    width: width * 0.8,
    height: 200,
  },
  collageItem: {
    borderRadius: 12,
    overflow: 'hidden',
    margin: 4,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  largeItem: {
    width: 120,
    height: 90,
  },
  mediumItem: {
    width: 80,
    height: 60,
  },
  smallItem: {
    width: 60,
    height: 45,
  },
  collageImage: {
    width: '100%',
    height: '100%',
  },
  taglineContainer: {
    alignItems: 'center',
  },
  appName: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  tagline: {
    fontSize: 18,
    color: '#fff',
    textAlign: 'center',
    fontStyle: 'italic',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
});