import { StatusBar } from 'expo-status-bar';
import { enableScreens } from 'react-native-screens';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { useState, useEffect } from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import SplashScreen from './src/screens/SplashScreen';
import LandingScreen from './src/screens/LandingScreen';
import HomeScreen from './src/screens/HomeScreen';
import ProfileScreen from './src/screens/ProfileScreen';
import ExploreScreen from './src/screens/ExploreScreen';
import BadgeDetailScreen from './src/screens/BadgeDetailScreen';
import RegisterEmailScreen from './src/screens/RegisterEmailScreen';
import RegisterDetailsScreen from './src/screens/RegisterDetailsScreen';
import EmailVerificationScreen from './src/screens/EmailVerificationScreen';
import LoginScreen from './src/screens/LoginScreen';
import ForgotPasswordScreen from './src/screens/ForgotPasswordScreen';
import AdminDashboard from './src/screens/Admin/Dashboard';
import UserList from './src/screens/Admin/UserList';
import BadgeCreate from './src/screens/Admin/BadgeCreateModern';
import { Provider } from 'react-redux';
import { Provider as PaperProvider } from 'react-native-paper';
import { ThemeProvider } from './src/contexts/ThemeContext';
import { LocationProvider } from './src/contexts/LocationContext';
import { store } from './src/store/store';
import { useAuth } from './src/hooks/useAuth';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from './src/contexts/ThemeContext';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

enableScreens();

export type RootStackParamList = {
  Splash: undefined;
  Landing: undefined;
  Home: undefined;
  Profile: undefined;
  Explore: undefined;
  BadgeDetail: { badgeId: string };
  RegisterEmail: undefined;
  RegisterProfile: { email: string };
  EmailVerification: { email: string };
  Login: undefined;
  ForgotPassword: undefined;
  AdminDashboard: undefined;
  UserList: undefined;
  BadgeCreate: { badgeId?: string };
};

export type MainTabParamList = {
  HomeTab: undefined;
  ExploreTab: undefined;
  ProfileTab: undefined;
  AdminTab: undefined;
};

const Stack = createNativeStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<MainTabParamList>();

function MainTabs() {
  const { user } = useAuth();
  const { theme } = useTheme();
  const insets = useSafeAreaInsets();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          if (route.name === 'HomeTab') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'ExploreTab') {
            iconName = focused ? 'map' : 'map-outline';
          } else if (route.name === 'ProfileTab') {
            iconName = focused ? 'person-outline' : 'person-outline';
          } else if (route.name === 'AdminTab') {
            iconName = focused ? 'shield' : 'shield-outline';
          } else {
            iconName = 'home-outline';
          }

          return <Ionicons name={iconName as any} size={size} color={color} />;
        },
        tabBarActiveTintColor: theme.colors.primaryAccent,
        tabBarInactiveTintColor: theme.colors.textSecondary,
        tabBarStyle: {
          backgroundColor: theme.colors.surface,
          borderTopColor: theme.colors.borderLight,
          height: 60 + insets.bottom,
          paddingBottom: insets.bottom,
          paddingTop: 8,
        },
        tabBarHideOnKeyboard: true,
        headerShown: false,
      })}
    >
      <Tab.Screen 
        name="HomeTab" 
        component={HomeScreen} 
        options={{ 
          title: 'Home',
          tabBarLabel: 'Home',
        }} 
      />
      <Tab.Screen 
        name="ExploreTab" 
        component={ExploreScreen} 
        options={{ 
          title: 'Explore',
          tabBarLabel: 'Explore',
        }} 
      />
      <Tab.Screen 
        name="ProfileTab" 
        component={ProfileScreen} 
        options={{ 
          title: 'Profile',
          tabBarLabel: 'Profile',
        }} 
      />
      {user?.role === 'admin' && (
        <Tab.Screen 
          name="AdminTab" 
          component={AdminDashboard} 
          options={{ 
            title: 'Manage',
            tabBarLabel: 'Manage',
          }} 
        />
      )}
    </Tab.Navigator>
  );
}

function AppNavigator() {
  const { isAuthenticated, user, loadUserFromStorage } = useAuth();

  useEffect(() => {
    loadUserFromStorage();
  }, []);

  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName={isAuthenticated ? 'Home' : 'Landing'}
        screenOptions={{
          headerShown: false,
        }}>
        {isAuthenticated ? (
          // Authenticated screens
          <>
            <Stack.Screen name="Home" component={MainTabs} />
            <Stack.Screen name="BadgeDetail" component={BadgeDetailScreen} />
            <Stack.Screen name="EmailVerification" component={EmailVerificationScreen} />
            <Stack.Screen name="UserList" component={UserList} />
            <Stack.Screen name="BadgeCreate" component={BadgeCreate} />
          </>
        ) : (
          // Unauthenticated screens
          <>
            <Stack.Screen name="Landing" component={LandingScreen} />
            <Stack.Screen name="RegisterEmail" component={RegisterEmailScreen} />
            <Stack.Screen name="RegisterProfile" component={RegisterDetailsScreen} />
            <Stack.Screen name="EmailVerification" component={EmailVerificationScreen} />
            <Stack.Screen name="Login" component={LoginScreen} />
            <Stack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
          </>
        )}
      </Stack.Navigator>
      <StatusBar style="auto" />
    </NavigationContainer>
  );
}

export default function App() {
  const [showSplash, setShowSplash] = useState(true);

  if (showSplash) {
    return <SplashScreen onFinish={() => setShowSplash(false)} />;
  }

  return (
    <SafeAreaProvider>
      <Provider store={store}>
        <ThemeProvider>
          <LocationProvider>
            <PaperProvider>
              <AppNavigator />
            </PaperProvider>
          </LocationProvider>
        </ThemeProvider>
      </Provider>
    </SafeAreaProvider>
  );
}
