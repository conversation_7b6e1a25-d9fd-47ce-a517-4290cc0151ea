import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { 
  Button, 
  Card, 
  Portal, 
  Modal, 
  FAB,
  SegmentedButtons,
  IconButton,
  Appbar
} from 'react-native-paper';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useForm, FormProvider } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { LinearGradient } from 'expo-linear-gradient';
import { lightTheme } from '../../config/theme';
import { useAuth } from '../../hooks/useAuth';
import { useLocation } from '../../contexts/LocationContext';
import { Badge, BadgeFormData, badgeService } from '../../services/BadgeService';
import BadgeDetailsTab from './BadgeCreate/DetailsTab';
import BadgeClueStoryTab from './BadgeCreate/ClueStoryTab';
import BadgeIconTab from './BadgeCreate/IconTab';
import BadgeLocationTab from './BadgeCreate/LocationTab';
import BadgePreview from './BadgeCreate/PreviewModal';

const validationSchema = yup.object({
  title: yup.string().required('Title is required').min(3, 'Title must be at least 3 characters'),
  clue: yup.string().required('Clue is required').min(10, 'Clue must be at least 10 characters'),
  story: yup.string().required('Story is required').min(20, 'Story must be at least 20 characters'),
  category: yup.string().required('Category is required'),
  unlockMethod: yup.string().oneOf(['gps', 'code']).required('Unlock method is required'),
  coords: yup.object({
    lat: yup.number().required('Latitude is required'),
    lng: yup.number().required('Longitude is required'),
  }).required('Location is required'),
  code: yup.string().when('unlockMethod', {
    is: 'code',
    then: (schema) => schema.required('Code is required when unlock method is code'),
    otherwise: (schema) => schema.optional(),
  }),
  icon: yup.string().required('Icon is required'),
  city: yup.array().of(yup.string()).min(1, 'At least one city must be selected'),
  state: yup.string().required('State is required'),
}) as yup.ObjectSchema<BadgeFormData>;

type TabValue = 'details' | 'clue' | 'icon' | 'location';

interface BadgeCreateProps {
  badgeId?: string;
}

export default function BadgeCreate() {
  const navigation = useNavigation();
  const route = useRoute();
  const { user } = useAuth();
  const { userLocation, isLocationLoading } = useLocation();
  const { badgeId } = route.params as BadgeCreateProps || {};
  
  const [activeTab, setActiveTab] = useState<TabValue>('details');
  const [loading, setLoading] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [selectedImage, setSelectedImage] = useState<any>(null);
  const [isEditMode, setIsEditMode] = useState(false);

  const methods = useForm<BadgeFormData>({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      title: '',
      clue: '',
      story: '',
      category: '',
      unlockMethod: 'gps',
      coords: { lat: 0, lng: 0 },
      code: '',
      icon: 'location-pin',
      city: [],
      state: '',
    },
  });

  const { handleSubmit, reset, watch, setValue, formState: { errors, isValid } } = methods;

  // Set user's current location as default for new badges
  useEffect(() => {
    if (!badgeId && userLocation && !isLocationLoading) {
      console.log('BadgeCreate (Classic): Setting user location as default:', userLocation);
      setValue('coords', { lat: userLocation.latitude, lng: userLocation.longitude });
      setValue('state', userLocation.state);
      if (userLocation.city) {
        setValue('city', [userLocation.city]);
      }
    }
  }, [userLocation, isLocationLoading, badgeId, setValue]);

  useEffect(() => {
    if (badgeId) {
      setIsEditMode(true);
      loadBadge();
    }
  }, [badgeId]);

  const loadBadge = async () => {
    try {
      setLoading(true);
      const badge = await badgeService.getBadgeById(badgeId!);
      if (badge) {
        reset({
          title: badge.title,
          clue: badge.clue,
          story: badge.story,
          category: badge.category,
          unlockMethod: badge.unlockMethod,
          coords: badge.coords,
          code: badge.code || '',
          icon: badge.icon,
          city: badge.city,
          state: badge.state,
        });
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to load badge data');
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: BadgeFormData) => {
    if (!user) {
      Alert.alert('Error', 'You must be logged in to create badges');
      return;
    }

    try {
      setLoading(true);
      
      if (isEditMode) {
        await badgeService.updateBadge(badgeId!, data, selectedImage);
        Alert.alert('Success', 'Badge updated successfully!');
      } else {
        await badgeService.createBadge(data, user.uid, selectedImage);
        Alert.alert('Success', 'Badge created successfully!');
      }
      
      navigation.goBack();
    } catch (error) {
      Alert.alert('Error', 'Failed to save badge. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!badgeId) return;

    Alert.alert(
      'Delete Badge',
      'Are you sure you want to delete this badge? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              setLoading(true);
              await badgeService.deleteBadge(badgeId);
              Alert.alert('Success', 'Badge deleted successfully!');
              navigation.goBack();
            } catch (error) {
              Alert.alert('Error', 'Failed to delete badge');
            } finally {
              setLoading(false);
            }
          },
        },
      ]
    );
  };

  const tabs = [
    { value: 'details', label: 'Details', icon: 'information' },
    { value: 'clue', label: 'Clue & Story', icon: 'book-open' },
    { value: 'icon', label: 'Icon', icon: 'image' },
    { value: 'location', label: 'Location', icon: 'map-marker' },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'details':
        return <BadgeDetailsTab />;
      case 'clue':
        return <BadgeClueStoryTab />;
      case 'icon':
        return <BadgeIconTab selectedImage={selectedImage} setSelectedImage={setSelectedImage} />;
      case 'location':
        return <BadgeLocationTab />;
      default:
        return null;
    }
  };

  const getTabErrors = (tab: TabValue) => {
    switch (tab) {
      case 'details':
        return !!(errors.title || errors.category || errors.state || errors.city);
      case 'clue':
        return !!(errors.clue || errors.story || errors.unlockMethod || errors.code);
      case 'icon':
        return !!errors.icon;
      case 'location':
        return !!errors.coords;
      default:
        return false;
    }
  };

  if (user?.role !== 'admin') {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Access Denied</Text>
          <Text style={styles.errorSubText}>You don't have permission to create badges.</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Appbar.Header style={styles.header}>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content title={isEditMode ? 'Edit Badge' : 'Create Badge'} />
        {isEditMode && (
          <Appbar.Action
            icon="delete"
            onPress={handleDelete}
            iconColor={lightTheme.colors.error}
          />
        )}
      </Appbar.Header>

      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <FormProvider {...methods}>
          <View style={styles.tabContainer}>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {tabs.map((tab) => (
                <Button
                  key={tab.value}
                  mode={activeTab === tab.value ? 'contained' : 'outlined'}
                  onPress={() => setActiveTab(tab.value as TabValue)}
                  style={[
                    styles.tabButton,
                    activeTab === tab.value && styles.activeTabButton,
                    getTabErrors(tab.value as TabValue) && styles.errorTabButton,
                  ]}
                  labelStyle={[
                    styles.tabLabel,
                    activeTab === tab.value && styles.activeTabLabel,
                  ]}
                  icon={tab.icon}
                  compact
                >
                  {tab.label}
                </Button>
              ))}
            </ScrollView>
          </View>

          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {renderTabContent()}
          </ScrollView>
        </FormProvider>
      </KeyboardAvoidingView>

      {/* Bottom Action Bar */}
      <LinearGradient
        colors={[lightTheme.colors.surface, lightTheme.colors.surfaceElevated] as any}
        style={styles.bottomBar}
      >
        <Button
          mode="outlined"
          onPress={() => setPreviewVisible(true)}
          style={styles.bottomButton}
          disabled={!isValid}
        >
          Preview
        </Button>
        <Button
          mode="contained"
          onPress={handleSubmit(onSubmit)}
          loading={loading}
          disabled={loading || !isValid}
          style={styles.bottomButton}
        >
          {isEditMode ? 'Update' : 'Save'}
        </Button>
      </LinearGradient>

      {/* Preview Modal */}
      <Portal>
        <BadgePreview
          visible={previewVisible}
          onDismiss={() => setPreviewVisible(false)}
          badgeData={watch()}
          selectedImage={selectedImage}
        />
      </Portal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: lightTheme.colors.background,
  },
  header: {
    backgroundColor: lightTheme.colors.surface,
    elevation: 2,
  },
  keyboardContainer: {
    flex: 1,
  },
  tabContainer: {
    backgroundColor: lightTheme.colors.surface,
    paddingVertical: lightTheme.spacing.sm,
    paddingHorizontal: lightTheme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: lightTheme.colors.borderLight,
  },
  tabButton: {
    marginRight: lightTheme.spacing.sm,
    minWidth: 100,
  },
  activeTabButton: {
    backgroundColor: lightTheme.colors.primaryAccent,
  },
  errorTabButton: {
    borderColor: lightTheme.colors.error,
  },
  tabLabel: {
    fontSize: 12,
    color: lightTheme.colors.textSecondary,
  },
  activeTabLabel: {
    color: lightTheme.colors.white,
  },
  content: {
    flex: 1,
    padding: lightTheme.spacing.lg,
  },
  bottomBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: lightTheme.spacing.lg,
    paddingVertical: lightTheme.spacing.md,
    borderTopWidth: 1,
    borderTopColor: lightTheme.colors.borderLight,
  },
  bottomButton: {
    flex: 1,
    marginHorizontal: lightTheme.spacing.sm,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: lightTheme.spacing.xl,
  },
  errorText: {
    ...lightTheme.typography.h4,
    color: lightTheme.colors.error,
    marginBottom: lightTheme.spacing.md,
  },
  errorSubText: {
    ...lightTheme.typography.body1,
    color: lightTheme.colors.textSecondary,
    textAlign: 'center',
  },
});