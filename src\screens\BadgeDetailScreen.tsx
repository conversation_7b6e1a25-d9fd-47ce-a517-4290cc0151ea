import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Image,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Card, Button, Dialog, Portal, TextInput, ProgressBar } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { doc, getDoc } from 'firebase/firestore';
import { useAudioPlayer } from 'expo-audio';
import { lightTheme } from '../config/theme';
import { db } from '../config/firebase';
import { useAuth } from '../hooks/useAuth';
import { useGeofence } from '../hooks/useGeofence';
import { UnlockService } from '../services/UnlockService';
import { Badge } from '../hooks/useBadgeFilter';

const { width } = Dimensions.get('window');

export default function BadgeDetailScreen() {
  const [badge, setBadge] = useState<Badge | null>(null);
  const [loading, setLoading] = useState(true);
  const [hintDialogVisible, setHintDialogVisible] = useState(false);
  const [noteDialogVisible, setNoteDialogVisible] = useState(false);
  const [personalNote, setPersonalNote] = useState('');
  const [isUnlocking, setIsUnlocking] = useState(false);
  
  const navigation = useNavigation();
  const route = useRoute();
  const { user } = useAuth();
  const { badgeId } = route.params as { badgeId: string };

  // Geofencing for unlock detection
  const geofence = useGeofence({
    latitude: badge?.location.latitude || 0,
    longitude: badge?.location.longitude || 0,
    radius: 50, // 50 meters
  });

  // Audio player for story narration
  const audioPlayer = useAudioPlayer(badge?.audioUrl || '');

  useEffect(() => {
    loadBadgeDetails();
  }, [badgeId]);

  const loadBadgeDetails = async () => {
    try {
      setLoading(true);
      
      // Load badge data
      const badgeDoc = await getDoc(doc(db, 'badges', badgeId));
      if (!badgeDoc.exists()) {
        Alert.alert('Error', 'Badge not found');
        navigation.goBack();
        return;
      }

      const badgeData = { id: badgeDoc.id, ...badgeDoc.data() } as Badge;

      // Check unlock status
      if (user) {
        const unlockStatus = await UnlockService.checkUnlockStatus(user.uid, badgeId);
        badgeData.isUnlocked = unlockStatus.isUnlocked;
        badgeData.viaHint = unlockStatus.viaHint;
        badgeData.unlockedAt = unlockStatus.unlockedAt;
      }

      setBadge(badgeData);
    } catch (error) {
      console.error('Error loading badge:', error);
      Alert.alert('Error', 'Failed to load badge details');
    } finally {
      setLoading(false);
    }
  };

  const handleUnlock = async (viaHint: boolean = false) => {
    if (!user || !badge) return;

    setIsUnlocking(true);
    try {
      const result = await UnlockService.unlockBadge(user.uid, badge.id, viaHint);
      
      if (result.success) {
        Alert.alert(
          'Success!',
          result.message,
          [{ text: 'OK', onPress: () => loadBadgeDetails() }]
        );
      } else {
        Alert.alert('Error', result.message);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to unlock badge. Please try again.');
    } finally {
      setIsUnlocking(false);
    }
  };

  const handleHintUnlock = () => {
    setHintDialogVisible(false);
    handleUnlock(true);
  };

  const handleShareBadge = () => {
    // In a real app, implement sharing functionality
    Alert.alert('Share', 'Share functionality would be implemented here');
  };

  const handleSaveNote = () => {
    // In a real app, save the note to Firestore
    setNoteDialogVisible(false);
    Alert.alert('Saved', 'Your personal note has been saved!');
  };

  const renderLockedView = () => {
    const proximityPercentage = geofence.getProximityPercentage();
    const canUnlock = geofence.isInRange && !geofence.isLoading;

    return (
      <View style={styles.lockedContainer}>
        {/* Clue Card */}
        <Card style={styles.clueCard}>
          <LinearGradient
            colors={[lightTheme.colors.surface, lightTheme.colors.surfaceElevated]}
            style={styles.clueGradient}
          >
            <View style={styles.clueHeader}>
              <Ionicons name="bulb-outline" size={24} color={lightTheme.colors.warning} />
              <Text style={styles.clueTitle}>Your Clue</Text>
            </View>
            <Text style={styles.clueText}>{badge?.clue}</Text>
          </LinearGradient>
        </Card>

        {/* Progress Bar */}
        <Card style={styles.progressCard}>
          <View style={styles.progressContent}>
            <View style={styles.progressHeader}>
              <Text style={styles.progressTitle}>Unlock Progress</Text>
              <Text style={styles.progressDistance}>{geofence.formatDistance()}</Text>
            </View>
            
            <ProgressBar
              progress={proximityPercentage / 100}
              color={canUnlock ? lightTheme.colors.success : lightTheme.colors.primaryAccent}
              style={styles.progressBar}
            />
            
            <Text style={styles.progressMessage}>
              {geofence.getProximityMessage()}
            </Text>

            {canUnlock && (
              <Button
                mode="contained"
                onPress={() => handleUnlock(false)}
                loading={isUnlocking}
                disabled={isUnlocking}
                style={styles.unlockButton}
                icon="unlock"
              >
                Unlock Badge
              </Button>
            )}
          </View>
        </Card>

        {/* Hint Button */}
        <Button
          mode="outlined"
          onPress={() => setHintDialogVisible(true)}
          style={styles.hintButton}
          icon="lightbulb-outline"
        >
          Need a Hint? (Half Star)
        </Button>
      </View>
    );
  };

  const renderUnlockedView = () => (
    <View style={styles.unlockedContainer}>
      {/* Achievement Banner */}
      <Card style={styles.achievementCard}>
        <LinearGradient
          colors={lightTheme.gradients.success}
          style={styles.achievementGradient}
        >
          <Ionicons name="trophy" size={32} color={lightTheme.colors.white} />
          <Text style={styles.achievementText}>Badge Unlocked!</Text>
          {badge?.viaHint && (
            <Text style={styles.hintNotice}>⭐ Unlocked with hint (Half star)</Text>
          )}
        </LinearGradient>
      </Card>

      {/* Story Content */}
      <Card style={styles.storyCard}>
        <View style={styles.storyContent}>
          <Text style={styles.storyTitle}>The Story</Text>
          
          {badge?.imageUrl && (
            <Image source={{ uri: badge.imageUrl }} style={styles.storyImage} />
          )}
          
          <Text style={styles.storyText}>{badge?.story}</Text>

          {/* Audio Controls */}
          {badge?.audioUrl && (
            <View style={styles.audioControls}>
              <Text style={styles.audioTitle}>Audio Story</Text>
              <View style={styles.audioButtons}>
                <Button
                  mode="contained"
                  onPress={() => audioPlayer.play()}
                  icon="play"
                  style={styles.audioButton}
                >
                  Play
                </Button>
                <Button
                  mode="outlined"
                  onPress={() => audioPlayer.pause()}
                  icon="pause"
                  style={styles.audioButton}
                >
                  Pause
                </Button>
              </View>
            </View>
          )}
        </View>
      </Card>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <Button
          mode="outlined"
          onPress={handleShareBadge}
          icon="share"
          style={styles.actionButton}
        >
          Share
        </Button>
        <Button
          mode="outlined"
          onPress={() => setNoteDialogVisible(true)}
          icon="note"
          style={styles.actionButton}
        >
          Add Note
        </Button>
      </View>
    </View>
  );

  if (loading || !badge) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading badge details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={lightTheme.colors.textPrimary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{badge.title}</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Badge Header */}
        <View style={styles.badgeHeader}>
          <LinearGradient
            colors={badge.isUnlocked ? 
              [lightTheme.colors.success, lightTheme.colors.accent5] :
              [lightTheme.colors.textSecondary, lightTheme.colors.borderLight]
            }
            style={styles.badgeIconLarge}
          >
            <Text style={styles.badgeIconTextLarge}>{badge.icon}</Text>
          </LinearGradient>
          
          <Text style={styles.badgeTitle}>{badge.title}</Text>
          <Text style={styles.badgeCategory}>{badge.category}</Text>
          <Text style={styles.badgeLocation}>{badge.location.address}</Text>
        </View>

        {/* Content */}
        {badge.isUnlocked ? renderUnlockedView() : renderLockedView()}
      </ScrollView>

      {/* Hint Dialog */}
      <Portal>
        <Dialog visible={hintDialogVisible} onDismiss={() => setHintDialogVisible(false)}>
          <Dialog.Title>Need a Hint?</Dialog.Title>
          <Dialog.Content>
            <Text style={styles.dialogText}>
              Using a hint will unlock this badge but you'll only earn half a star instead of a full star. Are you sure?
            </Text>
            <Text style={styles.hintText}>
              💡 Hint: {badge.hint || "Look for the distinctive architecture near the main entrance."}
            </Text>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setHintDialogVisible(false)}>Cancel</Button>
            <Button onPress={handleHintUnlock}>Use Hint</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      {/* Note Dialog */}
      <Portal>
        <Dialog visible={noteDialogVisible} onDismiss={() => setNoteDialogVisible(false)}>
          <Dialog.Title>Personal Note</Dialog.Title>
          <Dialog.Content>
            <TextInput
              label="Write your memory or thoughts..."
              value={personalNote}
              onChangeText={setPersonalNote}
              multiline
              numberOfLines={4}
              style={styles.noteInput}
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setNoteDialogVisible(false)}>Cancel</Button>
            <Button onPress={handleSaveNote}>Save</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: lightTheme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: lightTheme.spacing.lg,
    paddingVertical: lightTheme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: lightTheme.colors.borderLight,
  },
  headerTitle: {
    ...lightTheme.typography.h5,
    color: lightTheme.colors.textPrimary,
    fontWeight: '600',
    flex: 1,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 24,
  },
  scrollView: {
    flex: 1,
  },
  badgeHeader: {
    alignItems: 'center',
    paddingVertical: lightTheme.spacing.xl,
    paddingHorizontal: lightTheme.spacing.lg,
  },
  badgeIconLarge: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: lightTheme.spacing.lg,
    ...lightTheme.shadows.large,
  },
  badgeIconTextLarge: {
    fontSize: 48,
  },
  badgeTitle: {
    ...lightTheme.typography.h3,
    color: lightTheme.colors.textPrimary,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: lightTheme.spacing.sm,
  },
  badgeCategory: {
    ...lightTheme.typography.body1,
    color: lightTheme.colors.primaryAccent,
    fontWeight: '600',
    marginBottom: lightTheme.spacing.xs,
  },
  badgeLocation: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textSecondary,
    textAlign: 'center',
  },
  lockedContainer: {
    paddingHorizontal: lightTheme.spacing.lg,
    paddingBottom: lightTheme.spacing.xl,
  },
  clueCard: {
    marginBottom: lightTheme.spacing.lg,
    borderRadius: lightTheme.borderRadius.lg,
    overflow: 'hidden',
  },
  clueGradient: {
    padding: lightTheme.spacing.lg,
  },
  clueHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: lightTheme.spacing.md,
    gap: lightTheme.spacing.sm,
  },
  clueTitle: {
    ...lightTheme.typography.h6,
    color: lightTheme.colors.textPrimary,
    fontWeight: '600',
  },
  clueText: {
    ...lightTheme.typography.body1,
    color: lightTheme.colors.textPrimary,
    lineHeight: 24,
    fontStyle: 'italic',
  },
  progressCard: {
    marginBottom: lightTheme.spacing.lg,
    borderRadius: lightTheme.borderRadius.lg,
  },
  progressContent: {
    padding: lightTheme.spacing.lg,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: lightTheme.spacing.md,
  },
  progressTitle: {
    ...lightTheme.typography.h6,
    color: lightTheme.colors.textPrimary,
    fontWeight: '600',
  },
  progressDistance: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textSecondary,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    marginBottom: lightTheme.spacing.md,
  },
  progressMessage: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: lightTheme.spacing.lg,
  },
  unlockButton: {
    backgroundColor: lightTheme.colors.success,
  },
  hintButton: {
    borderColor: lightTheme.colors.warning,
  },
  unlockedContainer: {
    paddingHorizontal: lightTheme.spacing.lg,
    paddingBottom: lightTheme.spacing.xl,
  },
  achievementCard: {
    marginBottom: lightTheme.spacing.lg,
    borderRadius: lightTheme.borderRadius.lg,
    overflow: 'hidden',
  },
  achievementGradient: {
    padding: lightTheme.spacing.lg,
    alignItems: 'center',
  },
  achievementText: {
    ...lightTheme.typography.h5,
    color: lightTheme.colors.white,
    fontWeight: '700',
    marginTop: lightTheme.spacing.sm,
  },
  hintNotice: {
    ...lightTheme.typography.caption,
    color: lightTheme.colors.white,
    marginTop: lightTheme.spacing.xs,
    opacity: 0.9,
  },
  storyCard: {
    marginBottom: lightTheme.spacing.lg,
    borderRadius: lightTheme.borderRadius.lg,
  },
  storyContent: {
    padding: lightTheme.spacing.lg,
  },
  storyTitle: {
    ...lightTheme.typography.h5,
    color: lightTheme.colors.textPrimary,
    fontWeight: '600',
    marginBottom: lightTheme.spacing.md,
  },
  storyImage: {
    width: '100%',
    height: 200,
    borderRadius: lightTheme.borderRadius.md,
    marginBottom: lightTheme.spacing.lg,
  },
  storyText: {
    ...lightTheme.typography.body1,
    color: lightTheme.colors.textPrimary,
    lineHeight: 24,
    marginBottom: lightTheme.spacing.lg,
  },
  audioControls: {
    borderTopWidth: 1,
    borderTopColor: lightTheme.colors.borderLight,
    paddingTop: lightTheme.spacing.lg,
  },
  audioTitle: {
    ...lightTheme.typography.h6,
    color: lightTheme.colors.textPrimary,
    fontWeight: '600',
    marginBottom: lightTheme.spacing.md,
  },
  audioButtons: {
    flexDirection: 'row',
    gap: lightTheme.spacing.md,
  },
  audioButton: {
    flex: 1,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: lightTheme.spacing.md,
  },
  actionButton: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    ...lightTheme.typography.body1,
    color: lightTheme.colors.textSecondary,
  },
  dialogText: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textPrimary,
    marginBottom: lightTheme.spacing.md,
  },
  hintText: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.warning,
    fontStyle: 'italic',
    backgroundColor: lightTheme.colors.warning + '10',
    padding: lightTheme.spacing.md,
    borderRadius: lightTheme.borderRadius.md,
  },
  noteInput: {
    backgroundColor: lightTheme.colors.surface,
  },
});