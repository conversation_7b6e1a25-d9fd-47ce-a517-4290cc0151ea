import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import * as Location from 'expo-location';
import { Alert } from 'react-native';

interface LocationData {
  latitude: number;
  longitude: number;
  address: string;
  city: string;
  state: string;
  country: string;
  timestamp: number;
}

interface LocationContextType {
  userLocation: LocationData | null;
  isLocationLoading: boolean;
  locationError: string | null;
  requestLocation: () => Promise<void>;
  updateLocation: (location: LocationData) => void;
  hasLocationPermission: boolean;
}

const LocationContext = createContext<LocationContextType | undefined>(undefined);

interface LocationProviderProps {
  children: ReactNode;
}

export const LocationProvider: React.FC<LocationProviderProps> = ({ children }) => {
  const [userLocation, setUserLocation] = useState<LocationData | null>(null);
  const [isLocationLoading, setIsLocationLoading] = useState(false);
  const [locationError, setLocationError] = useState<string | null>(null);
  const [hasLocationPermission, setHasLocationPermission] = useState(false);

  // Request location permissions and get current location
  const requestLocation = async (): Promise<void> => {
    setIsLocationLoading(true);
    setLocationError(null);

    try {
      // Request permission
      const { status } = await Location.requestForegroundPermissionsAsync();
      setHasLocationPermission(status === 'granted');
      
      if (status !== 'granted') {
        setLocationError('Location permission denied');
        setIsLocationLoading(false);
        return;
      }

      // Get current position
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });

      // Reverse geocode to get address information
      const geocodeResults = await Location.reverseGeocodeAsync({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });

      const geocode = geocodeResults[0];
      
      const locationData: LocationData = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        address: geocode 
          ? `${geocode.street || ''} ${geocode.streetNumber || ''}, ${geocode.city || ''}, ${geocode.region || ''}, ${geocode.country || ''}`.replace(/\s+/g, ' ').trim()
          : `${location.coords.latitude.toFixed(6)}, ${location.coords.longitude.toFixed(6)}`,
        city: geocode?.city || geocode?.district || geocode?.subregion || '',
        state: geocode?.region || geocode?.administrativeArea || '',
        country: geocode?.country || '',
        timestamp: Date.now(),
      };

      setUserLocation(locationData);
      console.log('LocationContext: User location updated:', locationData);
      
    } catch (error) {
      console.error('LocationContext: Error getting location:', error);
      setLocationError('Failed to get current location');
      
      // Set fallback location for Odisha, India
      const fallbackLocation: LocationData = {
        latitude: 20.2961,
        longitude: 85.8245,
        address: 'Bhubaneswar, Odisha, India',
        city: 'Bhubaneswar',
        state: 'Odisha',
        country: 'India',
        timestamp: Date.now(),
      };
      
      setUserLocation(fallbackLocation);
      console.log('LocationContext: Using fallback location:', fallbackLocation);
    } finally {
      setIsLocationLoading(false);
    }
  };

  // Update location manually
  const updateLocation = (location: LocationData) => {
    setUserLocation(location);
    setLocationError(null);
  };

  // Auto-request location on app startup
  useEffect(() => {
    requestLocation();
  }, []);

  // Request location permission check on mount
  useEffect(() => {
    const checkPermission = async () => {
      const { status } = await Location.getForegroundPermissionsAsync();
      setHasLocationPermission(status === 'granted');
    };
    
    checkPermission();
  }, []);

  const value: LocationContextType = {
    userLocation,
    isLocationLoading,
    locationError,
    requestLocation,
    updateLocation,
    hasLocationPermission,
  };

  return (
    <LocationContext.Provider value={value}>
      {children}
    </LocationContext.Provider>
  );
};

export const useLocation = (): LocationContextType => {
  const context = useContext(LocationContext);
  if (context === undefined) {
    throw new Error('useLocation must be used within a LocationProvider');
  }
  return context;
};

export default LocationContext;