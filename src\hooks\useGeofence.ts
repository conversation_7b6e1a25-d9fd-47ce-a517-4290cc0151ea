import { useState, useEffect } from 'react';
import * as Location from 'expo-location';
import { getDistance } from 'geolib';

interface GeofenceOptions {
  latitude: number;
  longitude: number;
  radius: number; // in meters
}

interface GeofenceStatus {
  isInRange: boolean;
  distance: number;
  accuracy: number;
  isLoading: boolean;
  error: string | null;
}

export function useGeofence(options: GeofenceOptions) {
  const [status, setStatus] = useState<GeofenceStatus>({
    isInRange: false,
    distance: Infinity,
    accuracy: 0,
    isLoading: true,
    error: null,
  });

  const [subscription, setSubscription] = useState<Location.LocationSubscription | null>(null);

  useEffect(() => {
    let isMounted = true;

    const startLocationTracking = async () => {
      try {
        // Request permissions
        const { status: permissionStatus } = await Location.requestForegroundPermissionsAsync();
        
        if (permissionStatus !== 'granted') {
          if (isMounted) {
            setStatus(prev => ({
              ...prev,
              isLoading: false,
              error: 'Location permission denied',
            }));
          }
          return;
        }

        // Get initial location
        const initialLocation = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High,
        });

        const initialDistance = getDistance(
          {
            latitude: initialLocation.coords.latitude,
            longitude: initialLocation.coords.longitude,
          },
          {
            latitude: options.latitude,
            longitude: options.longitude,
          }
        );

        if (isMounted) {
          setStatus({
            isInRange: initialDistance <= options.radius,
            distance: initialDistance,
            accuracy: initialLocation.coords.accuracy || 0,
            isLoading: false,
            error: null,
          });
        }

        // Start watching location
        const sub = await Location.watchPositionAsync(
          {
            accuracy: Location.Accuracy.High,
            timeInterval: 5000, // Update every 5 seconds
            distanceInterval: 5, // Update every 5 meters
          },
          (location) => {
            if (!isMounted) return;

            const distance = getDistance(
              {
                latitude: location.coords.latitude,
                longitude: location.coords.longitude,
              },
              {
                latitude: options.latitude,
                longitude: options.longitude,
              }
            );

            setStatus({
              isInRange: distance <= options.radius,
              distance,
              accuracy: location.coords.accuracy || 0,
              isLoading: false,
              error: null,
            });
          }
        );

        setSubscription(sub);
      } catch (error) {
        if (isMounted) {
          setStatus(prev => ({
            ...prev,
            isLoading: false,
            error: error instanceof Error ? error.message : 'Location error',
          }));
        }
      }
    };

    startLocationTracking();

    return () => {
      isMounted = false;
      if (subscription) {
        subscription.remove();
      }
    };
  }, [options.latitude, options.longitude, options.radius]);

  const getProximityPercentage = () => {
    if (status.distance === Infinity) return 0;
    
    const maxDistance = options.radius * 2; // Consider 2x radius as 0%
    const percentage = Math.max(0, Math.min(100, 
      ((maxDistance - status.distance) / maxDistance) * 100
    ));
    
    return Math.round(percentage);
  };

  const getProximityMessage = () => {
    const percentage = getProximityPercentage();
    
    if (status.isInRange) {
      return "You're here! Ready to unlock?";
    } else if (percentage > 80) {
      return "Almost there! Keep going!";
    } else if (percentage > 60) {
      return "Getting closer...";
    } else if (percentage > 40) {
      return "You're on the right track";
    } else if (percentage > 20) {
      return "Keep exploring...";
    } else {
      return "Adventure awaits...";
    }
  };

  const formatDistance = () => {
    if (status.distance === Infinity) return "Unknown";
    
    if (status.distance < 1000) {
      return `${Math.round(status.distance)}m away`;
    } else {
      return `${(status.distance / 1000).toFixed(1)}km away`;
    }
  };

  return {
    ...status,
    getProximityPercentage,
    getProximityMessage,
    formatDistance,
    refresh: () => {
      // Force a location update
      if (subscription) {
        subscription.remove();
        setStatus(prev => ({ ...prev, isLoading: true }));
      }
    },
  };
}