import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../App';
import { lightTheme } from '../config/theme';
import { useAuth } from '../hooks/useAuth';
import { Ionicons } from '@expo/vector-icons';

type RegisterDetailsScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'RegisterProfile'>;

interface RouteParams {
  email: string;
}

export default function RegisterDetailsScreen() {
  const [username, setUsername] = useState('');
  const [fullName, setFullName] = useState('');
  const [mobile, setMobile] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  const [errors, setErrors] = useState({
    username: '',
    fullName: '',
    password: '',
    confirmPassword: '',
  });

  const navigation = useNavigation<RegisterDetailsScreenNavigationProp>();
  const route = useRoute();
  const { email } = route.params as RouteParams;
  const { completeRegistration, isLoading, error, clearError } = useAuth();

  const validateForm = () => {
    const newErrors = {
      username: '',
      fullName: '',
      password: '',
      confirmPassword: '',
    };

    // Username validation
    if (!username.trim()) {
      newErrors.username = 'Username is required';
    } else if (username.length < 3) {
      newErrors.username = 'Username must be at least 3 characters';
    } else if (!/^[a-zA-Z0-9_]+$/.test(username)) {
      newErrors.username = 'Username can only contain letters, numbers, and underscores';
    }

    // Full name validation
    if (!fullName.trim()) {
      newErrors.fullName = 'Full name is required';
    } else if (fullName.length < 2) {
      newErrors.fullName = 'Full name must be at least 2 characters';
    }

    // Password validation
    if (!password) {
      newErrors.password = 'Password is required';
    } else if (password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters';
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
      newErrors.password = 'Password must contain uppercase, lowercase, and number';
    }

    // Confirm password validation
    if (password !== confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    setErrors(newErrors);
    return !Object.values(newErrors).some(error => error !== '');
  };

  const handleInputChange = (field: string, value: string) => {
    switch (field) {
      case 'username':
        setUsername(value.toLowerCase().replace(/[^a-zA-Z0-9_]/g, ''));
        break;
      case 'fullName':
        setFullName(value);
        break;
      case 'mobile':
        setMobile(value.replace(/[^0-9+\-\s()]/g, ''));
        break;
      case 'password':
        setPassword(value);
        break;
      case 'confirmPassword':
        setConfirmPassword(value);
        break;
    }

    // Clear errors when user starts typing
    if (errors[field as keyof typeof errors]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
    if (error) clearError();
  };

  const getPasswordStrength = (password: string) => {
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/\d/.test(password)) strength++;
    if (/[^a-zA-Z0-9]/.test(password)) strength++;
    return strength;
  };

  const getPasswordStrengthText = (strength: number) => {
    switch (strength) {
      case 0:
      case 1:
        return { text: 'Weak', color: lightTheme.colors.error };
      case 2:
      case 3:
        return { text: 'Medium', color: lightTheme.colors.warning };
      case 4:
      case 5:
        return { text: 'Strong', color: lightTheme.colors.success };
      default:
        return { text: '', color: lightTheme.colors.textSecondary };
    }
  };

  const handleComplete = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      const result = await completeRegistration({
        email,
        username,
        fullName,
        mobile: mobile || undefined,
        password,
      });

      if (result.meta.requestStatus === 'fulfilled') {
        navigation.navigate('EmailVerification', { email });
      }
    } catch (err) {
      // Error is handled by Redux
    }
  };

  const passwordStrength = getPasswordStrength(password);
  const passwordStrengthInfo = getPasswordStrengthText(passwordStrength);

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <LinearGradient
            colors={[lightTheme.colors.surface, lightTheme.colors.surfaceElevated] as any}
            style={styles.header}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
          >
            <View style={styles.heroIcon}>
              <Text style={styles.heroIconText}>🏃‍♂️</Text>
            </View>
            <Text style={styles.title}>Almost There!</Text>
            <Text style={styles.subtitle}>
              Complete your explorer profile to unlock amazing quests
            </Text>
          </LinearGradient>

          <View style={styles.form}>
            {/* Username */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>🏷️ Username</Text>
              <TextInput
                style={[styles.input, errors.username && styles.inputError]}
                placeholder="adventurer_123"
                placeholderTextColor={lightTheme.colors.textSecondary}
                value={username}
                onChangeText={(text) => handleInputChange('username', text)}
                autoCapitalize="none"
                autoCorrect={false}
                editable={!isLoading}
              />
              {errors.username ? (
                <Text style={styles.errorText}>{errors.username}</Text>
              ) : (
                <Text style={styles.helpText}>Your unique explorer identity</Text>
              )}
            </View>

            {/* Full Name */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>👤 Full Name</Text>
              <TextInput
                style={[styles.input, errors.fullName && styles.inputError]}
                placeholder="Your Full Name"
                placeholderTextColor={lightTheme.colors.textSecondary}
                value={fullName}
                onChangeText={(text) => handleInputChange('fullName', text)}
                autoCapitalize="words"
                editable={!isLoading}
              />
              {errors.fullName && (
                <Text style={styles.errorText}>{errors.fullName}</Text>
              )}
            </View>

            {/* Mobile (Optional) */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>📱 Mobile Number (Optional)</Text>
              <TextInput
                style={styles.input}
                placeholder="+****************"
                placeholderTextColor={lightTheme.colors.textSecondary}
                value={mobile}
                onChangeText={(text) => handleInputChange('mobile', text)}
                keyboardType="phone-pad"
                editable={!isLoading}
              />
              <Text style={styles.helpText}>For quest notifications and emergency contact</Text>
            </View>

            {/* Password */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>🔒 Password</Text>
              <View style={styles.passwordContainer}>
                <TextInput
                  style={[styles.passwordInput, errors.password && styles.inputError]}
                  placeholder="Create a strong password"
                  placeholderTextColor={lightTheme.colors.textSecondary}
                  value={password}
                  onChangeText={(text) => handleInputChange('password', text)}
                  secureTextEntry={!showPassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                  editable={!isLoading}
                />
                <TouchableOpacity
                  style={styles.passwordToggle}
                  onPress={() => setShowPassword(!showPassword)}
                >
                  <Ionicons
                    name={showPassword ? 'eye-off' : 'eye'}
                    size={24}
                    color={lightTheme.colors.textSecondary}
                  />
                </TouchableOpacity>
              </View>
              {password.length > 0 && (
                <View style={styles.passwordStrengthContainer}>
                  <View style={styles.passwordStrengthBar}>
                    {[...Array(5)].map((_, index) => (
                      <View
                        key={index}
                        style={[
                          styles.passwordStrengthSegment,
                          index < passwordStrength && {
                            backgroundColor: passwordStrengthInfo.color,
                          },
                        ]}
                      />
                    ))}
                  </View>
                  <Text style={[styles.passwordStrengthText, { color: passwordStrengthInfo.color }]}>
                    {passwordStrengthInfo.text}
                  </Text>
                </View>
              )}
              {errors.password && (
                <Text style={styles.errorText}>{errors.password}</Text>
              )}
            </View>

            {/* Confirm Password */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>🔐 Confirm Password</Text>
              <View style={styles.passwordContainer}>
                <TextInput
                  style={[styles.passwordInput, errors.confirmPassword && styles.inputError]}
                  placeholder="Confirm your password"
                  placeholderTextColor={lightTheme.colors.textSecondary}
                  value={confirmPassword}
                  onChangeText={(text) => handleInputChange('confirmPassword', text)}
                  secureTextEntry={!showConfirmPassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                  editable={!isLoading}
                />
                <TouchableOpacity
                  style={styles.passwordToggle}
                  onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  <Ionicons
                    name={showConfirmPassword ? 'eye-off' : 'eye'}
                    size={24}
                    color={lightTheme.colors.textSecondary}
                  />
                </TouchableOpacity>
              </View>
              {errors.confirmPassword && (
                <Text style={styles.errorText}>{errors.confirmPassword}</Text>
              )}
            </View>

            {error && (
              <View style={styles.globalErrorContainer}>
                <Text style={styles.globalErrorText}>{error}</Text>
              </View>
            )}

            <TouchableOpacity
              style={styles.completeButtonContainer}
              onPress={handleComplete}
              disabled={isLoading}
            >
              <LinearGradient
                colors={isLoading ? [lightTheme.colors.textSecondary, lightTheme.colors.textSecondary] : lightTheme.gradients.success as any}
                style={styles.completeButton}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                <Text style={styles.completeButtonText}>
                  {isLoading ? '⚡ Creating Profile...' : '🎯 Complete Registration'}
                </Text>
              </LinearGradient>
            </TouchableOpacity>

            <View style={styles.infoContainer}>
              <LinearGradient
                colors={[lightTheme.colors.info + '20', lightTheme.colors.info + '10'] as any}
                style={styles.infoBox}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                <Text style={styles.infoText}>
                  🛡️ Your information is secure and will only be used to enhance your quest experience
                </Text>
              </LinearGradient>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: lightTheme.colors.background,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: lightTheme.spacing.lg,
  },
  header: {
    alignItems: 'center',
    marginTop: lightTheme.spacing.lg,
    marginBottom: lightTheme.spacing.xl,
    paddingVertical: lightTheme.spacing.xl,
    paddingHorizontal: lightTheme.spacing.lg,
    borderRadius: lightTheme.borderRadius.xxl,
    marginHorizontal: lightTheme.spacing.md,
    ...lightTheme.shadows.large,
  },
  heroIcon: {
    backgroundColor: lightTheme.colors.success + '20',
    borderRadius: lightTheme.borderRadius.round,
    width: 80,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: lightTheme.spacing.lg,
  },
  heroIconText: {
    fontSize: 40,
  },
  title: {
    ...lightTheme.typography.display2,
    color: lightTheme.colors.textPrimary,
    textAlign: 'center',
    marginBottom: lightTheme.spacing.md,
  },
  subtitle: {
    ...lightTheme.typography.body1,
    color: lightTheme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  form: {
    paddingBottom: lightTheme.spacing.xxl,
  },
  inputContainer: {
    marginBottom: lightTheme.spacing.lg,
  },
  inputLabel: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textPrimary,
    marginBottom: lightTheme.spacing.sm,
    fontWeight: '600',
  },
  input: {
    backgroundColor: lightTheme.colors.surface,
    borderRadius: lightTheme.borderRadius.xl,
    paddingHorizontal: lightTheme.spacing.lg,
    paddingVertical: lightTheme.spacing.lg,
    fontSize: 16,
    color: lightTheme.colors.textPrimary,
    borderWidth: 2,
    borderColor: lightTheme.colors.borderLight,
    ...lightTheme.shadows.medium,
  },
  inputError: {
    borderColor: lightTheme.colors.error,
  },
  passwordContainer: {
    position: 'relative',
  },
  passwordInput: {
    backgroundColor: lightTheme.colors.surface,
    borderRadius: lightTheme.borderRadius.xl,
    paddingHorizontal: lightTheme.spacing.lg,
    paddingVertical: lightTheme.spacing.lg,
    paddingRight: lightTheme.spacing.xxxl,
    fontSize: 16,
    color: lightTheme.colors.textPrimary,
    borderWidth: 2,
    borderColor: lightTheme.colors.borderLight,
    ...lightTheme.shadows.medium,
  },
  passwordToggle: {
    position: 'absolute',
    right: lightTheme.spacing.lg,
    top: lightTheme.spacing.lg,
    padding: lightTheme.spacing.xs,
  },
  passwordStrengthContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: lightTheme.spacing.sm,
    marginLeft: lightTheme.spacing.md,
  },
  passwordStrengthBar: {
    flexDirection: 'row',
    flex: 1,
    height: 4,
    backgroundColor: lightTheme.colors.borderLight,
    borderRadius: 2,
    marginRight: lightTheme.spacing.md,
    gap: 2,
  },
  passwordStrengthSegment: {
    flex: 1,
    height: '100%',
    backgroundColor: lightTheme.colors.borderLight,
    borderRadius: 1,
  },
  passwordStrengthText: {
    ...lightTheme.typography.caption,
    fontWeight: '600',
    minWidth: 60,
  },
  errorText: {
    ...lightTheme.typography.caption,
    color: lightTheme.colors.error,
    marginTop: lightTheme.spacing.sm,
    marginLeft: lightTheme.spacing.md,
  },
  helpText: {
    ...lightTheme.typography.caption,
    color: lightTheme.colors.textSecondary,
    marginTop: lightTheme.spacing.sm,
    marginLeft: lightTheme.spacing.md,
  },
  globalErrorContainer: {
    backgroundColor: lightTheme.colors.error + '20',
    paddingHorizontal: lightTheme.spacing.lg,
    paddingVertical: lightTheme.spacing.md,
    borderRadius: lightTheme.borderRadius.lg,
    marginBottom: lightTheme.spacing.lg,
    borderWidth: 1,
    borderColor: lightTheme.colors.error + '40',
  },
  globalErrorText: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.error,
    textAlign: 'center',
    fontWeight: '600',
  },
  completeButtonContainer: {
    borderRadius: lightTheme.borderRadius.xl,
    overflow: 'hidden',
    marginBottom: lightTheme.spacing.lg,
    ...lightTheme.shadows.glow,
  },
  completeButton: {
    paddingVertical: lightTheme.spacing.lg,
    alignItems: 'center',
  },
  completeButtonText: {
    ...lightTheme.typography.button,
    color: lightTheme.colors.white,
    fontSize: 18,
    fontWeight: '700',
  },
  infoContainer: {
    alignItems: 'center',
    paddingHorizontal: lightTheme.spacing.md,
  },
  infoBox: {
    paddingHorizontal: lightTheme.spacing.lg,
    paddingVertical: lightTheme.spacing.md,
    borderRadius: lightTheme.borderRadius.lg,
    width: '100%',
  },
  infoText: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.info,
    textAlign: 'center',
    lineHeight: 22,
    fontWeight: '500',
  },
});