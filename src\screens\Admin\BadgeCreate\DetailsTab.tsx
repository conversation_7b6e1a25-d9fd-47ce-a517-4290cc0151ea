import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { 
  TextInput, 
  Button, 
  Card, 
  Chip,
  Portal,
  Modal,
  List,
  Checkbox,
  Divider,
  RadioButton,
} from 'react-native-paper';
import { useFormContext, Controller } from 'react-hook-form';
import { LinearGradient } from 'expo-linear-gradient';
import { lightTheme } from '../../../config/theme';
import { BadgeFormData, BADGE_CATEGORIES, badgeService } from '../../../services/BadgeService';
import { Ionicons } from '@expo/vector-icons';

export default function BadgeDetailsTab() {
  const { control, watch, setValue, formState: { errors } } = useFormContext<BadgeFormData>();
  const [categoryModalVisible, setCategoryModalVisible] = useState(false);
  const [stateModalVisible, setStateModalVisible] = useState(false);
  const [cityModalVisible, setCityModalVisible] = useState(false);

  const selectedState = watch('state');
  const selectedCities = watch('city');
  const selectedCategory = watch('category');
  const unlockMethod = watch('unlockMethod');

  const cities = selectedState ? badgeService.getCitiesByState(selectedState) : [];
  const states = badgeService.getStates();

  const handleCityToggle = (city: string) => {
    const currentCities = selectedCities || [];
    const updatedCities = currentCities.includes(city)
      ? currentCities.filter(c => c !== city)
      : [...currentCities, city];
    
    setValue('city', updatedCities);
  };

  const getCategoryInfo = (categoryId: string) => {
    return BADGE_CATEGORIES.find(cat => cat.id === categoryId);
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.sectionTitle}>📝 Basic Information</Text>
          
          {/* Title Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Badge Title</Text>
            <Controller
              name="title"
              control={control}
              render={({ field: { onChange, value } }) => (
                <TextInput
                  mode="outlined"
                  value={value}
                  onChangeText={onChange}
                  placeholder="Enter badge title..."
                  style={styles.textInput}
                  error={!!errors.title}
                />
              )}
            />
            {errors.title && (
              <Text style={styles.errorText}>{errors.title.message}</Text>
            )}
          </View>

          {/* Category Picker */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Category</Text>
            <Button
              mode="outlined"
              onPress={() => setCategoryModalVisible(true)}
              style={[styles.pickerButton, errors.category && styles.errorInput]}
              contentStyle={styles.pickerContent}
            >
              {selectedCategory ? (
                <View style={styles.selectedCategoryContent}>
                  <Ionicons 
                    name={getCategoryInfo(selectedCategory)?.icon as any} 
                    size={20} 
                    color={lightTheme.colors.textPrimary} 
                  />
                  <Text style={styles.selectedCategoryText}>
                    {getCategoryInfo(selectedCategory)?.name}
                  </Text>
                </View>
              ) : (
                <Text style={styles.placeholderText}>Select Category</Text>
              )}
            </Button>
            {errors.category && (
              <Text style={styles.errorText}>{errors.category.message}</Text>
            )}
          </View>

          {/* Unlock Method */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Unlock Method</Text>
            <Controller
              name="unlockMethod"
              control={control}
              render={({ field: { onChange, value } }) => (
                <RadioButton.Group onValueChange={onChange} value={value}>
                  <View style={styles.radioContainer}>
                    <RadioButton.Item 
                      label="GPS Location" 
                      value="gps"
                      labelStyle={styles.radioLabel}
                    />
                    <RadioButton.Item 
                      label="Secret Code" 
                      value="code"
                      labelStyle={styles.radioLabel}
                    />
                  </View>
                </RadioButton.Group>
              )}
            />
          </View>

          {/* Code Input (only if unlock method is code) */}
          {unlockMethod === 'code' && (
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Secret Code</Text>
              <Controller
                name="code"
                control={control}
                render={({ field: { onChange, value } }) => (
                  <TextInput
                    mode="outlined"
                    value={value}
                    onChangeText={onChange}
                    placeholder="Enter secret code..."
                    style={styles.textInput}
                    error={!!errors.code}
                    autoCapitalize="characters"
                  />
                )}
              />
              {errors.code && (
                <Text style={styles.errorText}>{errors.code.message}</Text>
              )}
            </View>
          )}
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.sectionTitle}>🌍 Location Details</Text>
          
          {/* State Picker */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>State</Text>
            <Button
              mode="outlined"
              onPress={() => setStateModalVisible(true)}
              style={[styles.pickerButton, errors.state && styles.errorInput]}
              contentStyle={styles.pickerContent}
            >
              <Text style={selectedState ? styles.selectedText : styles.placeholderText}>
                {selectedState || 'Select State'}
              </Text>
            </Button>
            {errors.state && (
              <Text style={styles.errorText}>{errors.state.message}</Text>
            )}
          </View>

          {/* City Picker */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Cities</Text>
            <Button
              mode="outlined"
              onPress={() => setCityModalVisible(true)}
              style={[styles.pickerButton, errors.city && styles.errorInput]}
              contentStyle={styles.pickerContent}
              disabled={!selectedState}
            >
              <Text style={selectedCities?.length ? styles.selectedText : styles.placeholderText}>
                {selectedCities?.length 
                  ? `${selectedCities.length} cities selected` 
                  : 'Select Cities'
                }
              </Text>
            </Button>
            {errors.city && (
              <Text style={styles.errorText}>{errors.city.message}</Text>
            )}
            
            {/* Selected Cities Display */}
            {selectedCities?.length > 0 && (
              <View style={styles.selectedCitiesContainer}>
                {selectedCities.map((city) => (
                  <Chip
                    key={city}
                    onClose={() => handleCityToggle(city)}
                    style={styles.cityChip}
                    textStyle={styles.cityChipText}
                  >
                    {city}
                  </Chip>
                ))}
              </View>
            )}
          </View>
        </Card.Content>
      </Card>

      {/* Category Modal */}
      <Portal>
        <Modal
          visible={categoryModalVisible}
          onDismiss={() => setCategoryModalVisible(false)}
          contentContainerStyle={styles.modalContainer}
        >
          <Card>
            <Card.Title title="Select Category" />
            <Card.Content>
              <ScrollView style={styles.modalContent}>
                {BADGE_CATEGORIES.map((category) => (
                  <List.Item
                    key={category.id}
                    title={category.name}
                    left={(props) => (
                      <Ionicons 
                        name={category.icon as any} 
                        size={24} 
                        color={lightTheme.colors.textPrimary} 
                      />
                    )}
                    onPress={() => {
                      setValue('category', category.id);
                      setCategoryModalVisible(false);
                    }}
                    style={selectedCategory === category.id ? styles.selectedItem : undefined}
                  />
                ))}
              </ScrollView>
            </Card.Content>
          </Card>
        </Modal>
      </Portal>

      {/* State Modal */}
      <Portal>
        <Modal
          visible={stateModalVisible}
          onDismiss={() => setStateModalVisible(false)}
          contentContainerStyle={styles.modalContainer}
        >
          <Card>
            <Card.Title title="Select State" />
            <Card.Content>
              <ScrollView style={styles.modalContent}>
                {states.map((state) => (
                  <List.Item
                    key={state}
                    title={state}
                    onPress={() => {
                      setValue('state', state);
                      setValue('city', []); // Reset cities when state changes
                      setStateModalVisible(false);
                    }}
                    style={selectedState === state ? styles.selectedItem : undefined}
                  />
                ))}
              </ScrollView>
            </Card.Content>
          </Card>
        </Modal>
      </Portal>

      {/* City Modal */}
      <Portal>
        <Modal
          visible={cityModalVisible}
          onDismiss={() => setCityModalVisible(false)}
          contentContainerStyle={styles.modalContainer}
        >
          <Card>
            <Card.Title title="Select Cities" />
            <Card.Content>
              <ScrollView style={styles.modalContent}>
                {cities.map((city) => (
                  <List.Item
                    key={city}
                    title={city}
                    left={(props) => (
                      <Checkbox
                        status={selectedCities?.includes(city) ? 'checked' : 'unchecked'}
                        onPress={() => handleCityToggle(city)}
                      />
                    )}
                    onPress={() => handleCityToggle(city)}
                  />
                ))}
              </ScrollView>
            </Card.Content>
            <Card.Actions>
              <Button onPress={() => setCityModalVisible(false)}>Done</Button>
            </Card.Actions>
          </Card>
        </Modal>
      </Portal>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  card: {
    marginBottom: lightTheme.spacing.lg,
    borderRadius: lightTheme.borderRadius.lg,
  },
  sectionTitle: {
    ...lightTheme.typography.h6,
    color: lightTheme.colors.textPrimary,
    marginBottom: lightTheme.spacing.lg,
  },
  inputContainer: {
    marginBottom: lightTheme.spacing.lg,
  },
  inputLabel: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textPrimary,
    marginBottom: lightTheme.spacing.sm,
    fontWeight: '600',
  },
  textInput: {
    backgroundColor: lightTheme.colors.surface,
  },
  pickerButton: {
    justifyContent: 'flex-start',
    borderColor: lightTheme.colors.borderLight,
  },
  pickerContent: {
    justifyContent: 'flex-start',
    paddingVertical: lightTheme.spacing.sm,
  },
  selectedCategoryContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: lightTheme.spacing.sm,
  },
  selectedCategoryText: {
    ...lightTheme.typography.body1,
    color: lightTheme.colors.textPrimary,
  },
  selectedText: {
    ...lightTheme.typography.body1,
    color: lightTheme.colors.textPrimary,
  },
  placeholderText: {
    ...lightTheme.typography.body1,
    color: lightTheme.colors.textSecondary,
  },
  radioContainer: {
    gap: lightTheme.spacing.sm,
  },
  radioLabel: {
    ...lightTheme.typography.body1,
    color: lightTheme.colors.textPrimary,
  },
  selectedCitiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: lightTheme.spacing.sm,
    marginTop: lightTheme.spacing.md,
  },
  cityChip: {
    backgroundColor: lightTheme.colors.primaryAccent + '20',
  },
  cityChipText: {
    color: lightTheme.colors.primaryAccent,
  },
  modalContainer: {
    backgroundColor: lightTheme.colors.surface,
    margin: lightTheme.spacing.xl,
    borderRadius: lightTheme.borderRadius.lg,
    maxHeight: '70%',
  },
  modalContent: {
    maxHeight: 400,
  },
  selectedItem: {
    backgroundColor: lightTheme.colors.primaryAccent + '20',
  },
  errorInput: {
    borderColor: lightTheme.colors.error,
  },
  errorText: {
    ...lightTheme.typography.caption,
    color: lightTheme.colors.error,
    marginTop: lightTheme.spacing.sm,
  },
});