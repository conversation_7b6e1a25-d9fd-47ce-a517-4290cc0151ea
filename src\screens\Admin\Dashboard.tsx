import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Dimensions,
  Animated,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation } from '@react-navigation/native';
import { lightTheme } from '../../config/theme';
import { useAuth } from '../../hooks/useAuth';
import { collection, query, where, getDocs, getCountFromServer } from 'firebase/firestore';
import { db } from '../../config/firebase';
import { Ionicons } from '@expo/vector-icons';

const { width } = Dimensions.get('window');

interface AdminStats {
  totalUsers: number;
  activeUsers: number;
  totalAdmins: number;
  activeQuests: number;
  completedQuests: number;
  pendingSubmissions: number;
}

export default function AdminDashboard() {
  const navigation = useNavigation();
  const { user } = useAuth();
  const [stats, setStats] = useState<AdminStats>({
    totalUsers: 0,
    activeUsers: 0,
    totalAdmins: 0,
    activeQuests: 0,
    completedQuests: 0,
    pendingSubmissions: 0,
  });
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  
  // Animation values
  const fadeAnim = new Animated.Value(0);
  const slideAnim = new Animated.Value(50);

  useEffect(() => {
    // Start animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
    
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      const usersRef = collection(db, 'users');
      
      // Get total users
      const totalUsersQuery = query(usersRef);
      const totalUsersSnapshot = await getCountFromServer(totalUsersQuery);
      
      // Get active users
      const activeUsersQuery = query(usersRef, where('active', '==', true));
      const activeUsersSnapshot = await getCountFromServer(activeUsersQuery);
      
      // Get total admins
      const adminsQuery = query(usersRef, where('role', '==', 'admin'));
      const adminsSnapshot = await getCountFromServer(adminsQuery);
      
      setStats({
        totalUsers: totalUsersSnapshot.data().count,
        activeUsers: activeUsersSnapshot.data().count,
        totalAdmins: adminsSnapshot.data().count,
        activeQuests: 25,
        completedQuests: 184,
        pendingSubmissions: 7,
      });
    } catch (error) {
      console.error('Error fetching admin stats:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    fetchStats();
  };

  // Gaming-style stat card with holographic effect
  const StatCard = ({ title, value, icon, gradient, subtitle, delay = 0 }: {
    title: string;
    value: number;
    icon: string;
    gradient: string[];
    subtitle?: string;
    delay?: number;
  }) => {
    const cardAnim = new Animated.Value(0);
    
    React.useEffect(() => {
      Animated.timing(cardAnim, {
        toValue: 1,
        duration: 600,
        delay: delay * 100,
        useNativeDriver: true,
      }).start();
    }, []);

    return (
      <Animated.View
        style={[
          styles.statCard,
          {
            opacity: cardAnim,
            transform: [
              {
                translateY: cardAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [30, 0],
                }),
              },
            ],
          },
        ]}
      >
        <LinearGradient
          colors={gradient}
          style={styles.statCardGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.statCardOverlay}>
            <View style={styles.statCardContent}>
              <View style={styles.statIconContainer}>
                <View style={styles.statIconHalo}>
                  <Ionicons name={icon as any} size={28} color="#FFFFFF" />
                </View>
              </View>
              
              <View style={styles.statInfo}>
                <Text style={styles.statValue}>{value.toLocaleString()}</Text>
                <Text style={styles.statTitle}>{title}</Text>
                {subtitle && <Text style={styles.statSubtitle}>{subtitle}</Text>}
              </View>
            </View>
            
            {/* Gaming-style corner decorations */}
            <View style={styles.cornerDecoration} />
          </View>
        </LinearGradient>
      </Animated.View>
    );
  };

  // Gaming-style action button
  const ActionButton = ({ title, subtitle, icon, color, onPress, delay = 0 }: {
    title: string;
    subtitle: string;
    icon: string;
    color: string;
    onPress: () => void;
    delay?: number;
  }) => {
    const buttonAnim = new Animated.Value(0);
    
    React.useEffect(() => {
      Animated.timing(buttonAnim, {
        toValue: 1,
        duration: 600,
        delay: delay * 150,
        useNativeDriver: true,
      }).start();
    }, []);

    return (
      <Animated.View
        style={[
          styles.actionButton,
          {
            opacity: buttonAnim,
            transform: [
              {
                scale: buttonAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0.8, 1],
                }),
              },
            ],
          },
        ]}
      >
        <TouchableOpacity onPress={onPress} style={styles.actionButtonTouchable}>
          <LinearGradient
            colors={[color + '15', color + '05']}
            style={styles.actionButtonGradient}
          >
            <View style={styles.actionButtonContent}>
              <View style={[styles.actionIconContainer, { backgroundColor: color + '20' }]}>
                <Ionicons name={icon as any} size={32} color={color} />
              </View>
              <View style={styles.actionTextContainer}>
                <Text style={styles.actionTitle}>{title}</Text>
                <Text style={styles.actionSubtitle}>{subtitle}</Text>
              </View>
              <View style={styles.actionArrow}>
                <Ionicons name="chevron-forward" size={20} color={color} />
              </View>
            </View>
            
            {/* Cyber-style border effect */}
            <View style={[styles.actionBorder, { borderColor: color + '30' }]} />
          </LinearGradient>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  if (user?.role !== 'admin') {
    return (
      <SafeAreaView style={styles.container}>
        <LinearGradient
          colors={['#1a1a2e', '#16213e', '#0f3460']}
          style={styles.errorContainer}
        >
          <View style={styles.errorContent}>
            <Ionicons name="shield-outline" size={80} color="#ff6b6b" />
            <Text style={styles.errorText}>ACCESS DENIED</Text>
            <Text style={styles.errorSubText}>Administrator privileges required</Text>
          </View>
        </LinearGradient>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#0a0a23', '#1a1a2e', '#16213e']}
        style={styles.backgroundGradient}
      >
        <ScrollView
          style={styles.scrollView}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        >
          {/* Futuristic Header */}
          <Animated.View style={{ opacity: fadeAnim, transform: [{ translateY: slideAnim }] }}>
            <LinearGradient
              colors={['#667eea', '#764ba2', '#f093fb']}
              style={styles.header}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <View style={styles.headerOverlay}>
                <View style={styles.headerContent}>
                  <View style={styles.welcomeSection}>
                    <Text style={styles.gameTitle}>ADMIN CONTROL</Text>
                    <Text style={styles.gameSubtitle}>System Command Center</Text>
                    <Text style={styles.welcomeText}>Welcome back, {user?.fullName || 'Administrator'}</Text>
                  </View>
                  <View style={styles.adminBadge}>
                    <LinearGradient
                      colors={['#ffd700', '#ffed4e', '#fff59d']}
                      style={styles.adminBadgeGradient}
                    >
                      <Text style={styles.adminBadgeText}>👑</Text>
                    </LinearGradient>
                  </View>
                </View>
                
                {/* Decorative elements */}
                <View style={styles.headerDecoration} />
              </View>
            </LinearGradient>
          </Animated.View>

          <View style={styles.content}>
            {/* Stats Section */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>⚡ SYSTEM METRICS</Text>
                <Text style={styles.sectionSubtitle}>Real-time platform analytics</Text>
              </View>

              <View style={styles.statsGrid}>
                <StatCard
                  title="TOTAL USERS"
                  value={stats.totalUsers}
                  icon="people-outline"
                  gradient={['#667eea', '#764ba2']}
                  subtitle="Registered players"
                  delay={0}
                />
                <StatCard
                  title="ACTIVE NOW"
                  value={stats.activeUsers}
                  icon="pulse"
                  gradient={['#f093fb', '#f5576c']}
                  subtitle="Online players"
                  delay={1}
                />
                <StatCard
                  title="ADMINS"
                  value={stats.totalAdmins}
                  icon="shield"
                  gradient={['#4facfe', '#00f2fe']}
                  subtitle="System operators"
                  delay={2}
                />
                <StatCard
                  title="LIVE QUESTS"
                  value={stats.activeQuests}
                  icon="rocket"
                  gradient={['#43e97b', '#38f9d7']}
                  subtitle="Active missions"
                  delay={3}
                />
                <StatCard
                  title="COMPLETED"
                  value={stats.completedQuests}
                  icon="trophy"
                  gradient={['#fa709a', '#fee140']}
                  subtitle="Quest victories"
                  delay={4}
                />
                <StatCard
                  title="PENDING"
                  value={stats.pendingSubmissions}
                  icon="hourglass"
                  gradient={['#ff9a9e', '#fecfef']}
                  subtitle="Awaiting review"
                  delay={5}
                />
              </View>
            </View>

            {/* Action Commands Section */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>🎮 COMMAND CENTER</Text>
                <Text style={styles.sectionSubtitle}>Quick access to admin tools</Text>
              </View>

              <View style={styles.actionGrid}>
                <ActionButton
                  title="MANAGE USERS"
                  subtitle="Player administration"
                  icon="people-outline"
                  color="#667eea"
                  onPress={() => navigation.navigate('UserList' as never)}
                  delay={0}
                />
                
                <ActionButton
                  title="CREATE BADGE"
                  subtitle="Design achievements"
                  icon="trophy"
                  color="#f093fb"
                  onPress={() => navigation.navigate('BadgeCreate' as never)}
                  delay={1}
                />
                
                <ActionButton
                  title="QUEST BUILDER"
                  subtitle="Mission designer"
                  icon="construct"
                  color="#43e97b"
                  onPress={() => {}}
                  delay={2}
                />
                
                <ActionButton
                  title="ANALYTICS"
                  subtitle="Performance metrics"
                  icon="bar-chart"
                  color="#4facfe"
                  onPress={() => {}}
                  delay={3}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundGradient: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingVertical: 40,
    paddingHorizontal: 20,
    position: 'relative',
  },
  headerOverlay: {
    position: 'relative',
    zIndex: 1,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  welcomeSection: {
    flex: 1,
  },
  gameTitle: {
    fontSize: 28,
    fontWeight: '900',
    color: '#FFFFFF',
    letterSpacing: 2,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
  },
  gameSubtitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
    opacity: 0.8,
    letterSpacing: 1,
    marginBottom: 8,
  },
  welcomeText: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.9,
    fontWeight: '500',
  },
  adminBadge: {
    width: 70,
    height: 70,
    borderRadius: 35,
    overflow: 'hidden',
  },
  adminBadgeGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  adminBadgeText: {
    fontSize: 30,
  },
  headerDecoration: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(255,255,255,0.1)',
    transform: [{ translateX: 50 }, { translateY: -25 }],
  },
  content: {
    padding: 20,
  },
  section: {
    marginBottom: 30,
  },
  sectionHeader: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: '800',
    color: '#FFFFFF',
    letterSpacing: 1,
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.7,
    fontWeight: '500',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statCard: {
    width: '48%',
    marginBottom: 15,
    borderRadius: 16,
    overflow: 'hidden',
  },
  statCardGradient: {
    flex: 1,
  },
  statCardOverlay: {
    flex: 1,
    padding: 16,
    position: 'relative',
  },
  statCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statIconContainer: {
    marginRight: 12,
  },
  statIconHalo: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  statInfo: {
    flex: 1,
  },
  statValue: {
    fontSize: 24,
    fontWeight: '900',
    color: '#FFFFFF',
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  statTitle: {
    fontSize: 12,
    fontWeight: '700',
    color: '#FFFFFF',
    opacity: 0.9,
    letterSpacing: 0.5,
  },
  statSubtitle: {
    fontSize: 10,
    color: '#FFFFFF',
    opacity: 0.7,
    fontWeight: '500',
  },
  cornerDecoration: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 20,
    height: 20,
    borderTopWidth: 2,
    borderRightWidth: 2,
    borderColor: 'rgba(255,255,255,0.3)',
  },
  actionGrid: {
    gap: 12,
  },
  actionButton: {
    marginBottom: 12,
  },
  actionButtonTouchable: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  actionButtonGradient: {
    position: 'relative',
  },
  actionButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
  },
  actionIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  actionTextContainer: {
    flex: 1,
  },
  actionTitle: {
    fontSize: 18,
    fontWeight: '800',
    color: '#FFFFFF',
    letterSpacing: 0.5,
    marginBottom: 4,
  },
  actionSubtitle: {
    fontSize: 13,
    color: '#FFFFFF',
    opacity: 0.8,
    fontWeight: '500',
  },
  actionArrow: {
    marginLeft: 16,
  },
  actionBorder: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderWidth: 1,
    borderRadius: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContent: {
    alignItems: 'center',
    padding: 40,
  },
  errorText: {
    fontSize: 28,
    fontWeight: '900',
    color: '#ff6b6b',
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 10,
    letterSpacing: 2,
  },
  errorSubText: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.8,
    textAlign: 'center',
    fontWeight: '500',
  },
});