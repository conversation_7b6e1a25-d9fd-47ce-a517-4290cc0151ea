export const colors = {
  light: {
    background: '#F8FAFC',
    surface: '#FFFFFF',
    surfaceElevated: '#FAFBFC',
    primaryAccent: '#6366F1',
    primaryLight: '#A5B4FC',
    primaryDark: '#4338CA',
    secondaryAccent: '#F59E0B',
    secondaryLight: '#FCD34D',
    secondaryDark: '#D97706',
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',
    textPrimary: '#1F2937',
    textSecondary: '#6B7280',
    textTertiary: '#9CA3AF',
    textInverse: '#FFFFFF',
    shadow: '#000000',
    white: '#FFFFFF',
    black: '#000000',
    border: '#E5E7EB',
    borderLight: '#F3F4F6',
    accent1: '#8B5CF6',
    accent2: '#06B6D4',
    accent3: '#F97316',
    accent4: '#EF4444',
    accent5: '#10B981',
  },
  dark: {
    background: '#0F172A',
    surface: '#1E293B',
    surfaceElevated: '#334155',
    primaryAccent: '#818CF8',
    primaryLight: '#C7D2FE',
    primaryDark: '#6366F1',
    secondaryAccent: '#FBBF24',
    secondaryLight: '#FDE047',
    secondaryDark: '#F59E0B',
    success: '#34D399',
    warning: '#FBBF24',
    error: '#F87171',
    info: '#60A5FA',
    textPrimary: '#F8FAFC',
    textSecondary: '#CBD5E1',
    textTertiary: '#94A3B8',
    textInverse: '#1F2937',
    shadow: '#000000',
    white: '#FFFFFF',
    black: '#000000',
    border: '#374151',
    borderLight: '#4B5563',
    accent1: '#A78BFA',
    accent2: '#22D3EE',
    accent3: '#FB923C',
    accent4: '#F87171',
    accent5: '#34D399',
  },
};

export const fonts = {
  heading: {
    fontFamily: 'Poppins_600SemiBold',
    weights: {
      regular: '400' as const,
      medium: '500' as const,
      semibold: '600' as const,
      bold: '700' as const,
      extrabold: '800' as const,
    },
  },
  body: {
    fontFamily: 'Inter_400Regular',
    weights: {
      regular: '400' as const,
      medium: '500' as const,
      semibold: '600' as const,
    },
  },
  display: {
    fontFamily: 'DMSans_700Bold',
    weights: {
      regular: '400' as const,
      medium: '500' as const,
      bold: '700' as const,
    },
  },
};

export const typography = {
  display1: {
    fontFamily: fonts.display.fontFamily,
    fontWeight: fonts.display.weights.bold,
    fontSize: 48,
    lineHeight: 56,
    letterSpacing: -0.5,
  },
  display2: {
    fontFamily: fonts.display.fontFamily,
    fontWeight: fonts.display.weights.bold,
    fontSize: 36,
    lineHeight: 44,
    letterSpacing: -0.5,
  },
  h1: {
    fontFamily: fonts.heading.fontFamily,
    fontWeight: fonts.heading.weights.bold,
    fontSize: 32,
    lineHeight: 38,
    letterSpacing: -0.2,
  },
  h2: {
    fontFamily: fonts.heading.fontFamily,
    fontWeight: fonts.heading.weights.bold,
    fontSize: 28,
    lineHeight: 34,
    letterSpacing: -0.2,
  },
  h3: {
    fontFamily: fonts.heading.fontFamily,
    fontWeight: fonts.heading.weights.semibold,
    fontSize: 24,
    lineHeight: 30,
    letterSpacing: -0.1,
  },
  h4: {
    fontFamily: fonts.heading.fontFamily,
    fontWeight: fonts.heading.weights.semibold,
    fontSize: 20,
    lineHeight: 26,
    letterSpacing: -0.1,
  },
  h5: {
    fontFamily: fonts.heading.fontFamily,
    fontWeight: fonts.heading.weights.semibold,
    fontSize: 18,
    lineHeight: 24,
  },
  h6: {
    fontFamily: fonts.heading.fontFamily,
    fontWeight: fonts.heading.weights.semibold,
    fontSize: 16,
    lineHeight: 22,
  },
  body1: {
    fontFamily: fonts.body.fontFamily,
    fontWeight: fonts.body.weights.regular,
    fontSize: 16,
    lineHeight: 24,
  },
  body2: {
    fontFamily: fonts.body.fontFamily,
    fontWeight: fonts.body.weights.regular,
    fontSize: 14,
    lineHeight: 20,
  },
  caption: {
    fontFamily: fonts.body.fontFamily,
    fontWeight: fonts.body.weights.regular,
    fontSize: 12,
    lineHeight: 16,
  },
  button: {
    fontFamily: fonts.heading.fontFamily,
    fontWeight: fonts.heading.weights.semibold,
    fontSize: 16,
    lineHeight: 20,
    letterSpacing: 0.5,
  },
  buttonSmall: {
    fontFamily: fonts.heading.fontFamily,
    fontWeight: fonts.heading.weights.semibold,
    fontSize: 14,
    lineHeight: 18,
    letterSpacing: 0.5,
  },
  overline: {
    fontFamily: fonts.body.fontFamily,
    fontWeight: fonts.body.weights.semibold,
    fontSize: 12,
    lineHeight: 16,
    textTransform: 'uppercase' as const,
    letterSpacing: 1,
  },
  gaming: {
    fontFamily: fonts.display.fontFamily,
    fontWeight: fonts.display.weights.bold,
    fontSize: 24,
    lineHeight: 30,
    letterSpacing: 0.5,
  },
};

export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  xxxl: 64,
};

export const borderRadius = {
  sm: 6,
  md: 10,
  lg: 16,
  xl: 20,
  xxl: 28,
  xxxl: 36,
  round: 999,
};

export const shadows = {
  small: {
    shadowColor: colors.light.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  medium: {
    shadowColor: colors.light.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  },
  large: {
    shadowColor: colors.light.shadow,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
  glow: {
    shadowColor: colors.light.primaryAccent,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 6,
  },
  glowSecondary: {
    shadowColor: colors.light.secondaryAccent,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 6,
  },
};

export type Theme = {
  colors: typeof colors.light;
  fonts: typeof fonts;
  typography: typeof typography;
  spacing: typeof spacing;
  borderRadius: typeof borderRadius;
  shadows: typeof shadows;
  gradients: typeof gradients;
  animations: typeof animations;
};

export const gradients = {
  primary: ['#6366F1', '#8B5CF6'] as const,
  secondary: ['#F59E0B', '#EF4444'] as const,
  success: ['#10B981', '#059669'] as const,
  gaming: ['#8B5CF6', '#EC4899', '#F59E0B'] as const,
  quest: ['#3B82F6', '#06B6D4', '#10B981'] as const,
  achievement: ['#F59E0B', '#EF4444', '#EC4899'] as const,
  level: ['#6366F1', '#8B5CF6', '#A78BFA'] as const,
  dark: ['#1F2937', '#374151'] as const,
  light: ['#FFFFFF', '#F8FAFC'] as const,
};

export const animations = {
  spring: {
    tension: 300,
    friction: 20,
  },
  bounce: {
    tension: 200,
    friction: 12,
  },
  smooth: {
    duration: 200,
  },
  slow: {
    duration: 400,
  },
  fast: {
    duration: 100,
  },
};

export const createTheme = (isDark: boolean = false): Theme => ({
  colors: isDark ? colors.dark : colors.light,
  fonts,
  typography,
  spacing,
  borderRadius,
  shadows,
  gradients,
  animations,
});

export const lightTheme = createTheme(false);

export const createGradientStyle = (colors: string[], angle: number = 45) => ({
  background: `linear-gradient(${angle}deg, ${colors.join(', ')})`,
});

export const createGlowEffect = (color: string, intensity: number = 0.3) => ({
  shadowColor: color,
  shadowOffset: { width: 0, height: 4 },
  shadowOpacity: intensity,
  shadowRadius: 12,
  elevation: 8,
});
export const darkTheme = createTheme(true);

export const gameColors = {
  bronze: '#CD7F32',
  silver: '#C0C0C0',
  gold: '#FFD700',
  platinum: '#E5E4E2',
  diamond: '#B9F2FF',
  legendary: '#FF6B35',
  mythic: '#9B59B6',
  common: '#95A5A6',
  rare: '#3498DB',
  epic: '#9B59B6',
  legendary2: '#E74C3C',
  artifact: '#F39C12',
};

export const questDifficulty = {
  easy: {
    color: '#10B981',
    gradient: ['#10B981', '#059669'] as const,
    icon: '🟢',
  },
  medium: {
    color: '#F59E0B',
    gradient: ['#F59E0B', '#D97706'] as const,
    icon: '🟡',
  },
  hard: {
    color: '#EF4444',
    gradient: ['#EF4444', '#DC2626'] as const,
    icon: '🔴',
  },
  expert: {
    color: '#8B5CF6',
    gradient: ['#8B5CF6', '#7C3AED'] as const,
    icon: '🟣',
  },
  legendary: {
    color: '#EC4899',
    gradient: ['#EC4899', '#DB2777'] as const,
    icon: '⭐',
  },
};

export const levelBadges: { [key: number]: { name: string; color: string; icon: string } } = {
  1: { name: 'Novice Explorer', color: gameColors.bronze, icon: '🌱' },
  2: { name: 'Curious Wanderer', color: gameColors.bronze, icon: '🚶' },
  3: { name: 'City Navigator', color: gameColors.silver, icon: '🗺️' },
  4: { name: 'Quest Hunter', color: gameColors.silver, icon: '🏹' },
  5: { name: 'Urban Pioneer', color: gameColors.gold, icon: '🏆' },
  6: { name: 'Master Explorer', color: gameColors.platinum, icon: '👑' },
  7: { name: 'Legendary Adventurer', color: gameColors.diamond, icon: '💎' },
  8: { name: 'Mythic Champion', color: gameColors.mythic, icon: '🌟' },
};