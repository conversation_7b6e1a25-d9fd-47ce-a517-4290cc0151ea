import { doc, getDoc, setDoc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '../config/firebase';

export interface UserProfile {
  displayName: string;
  photoURL: string;
  email: string;
  role: string;
  city?: string;
  title?: string;
  level?: number;
  stats: {
    totalBadges: number;
    questsCompleted: number;
    hoursExplored?: number;
    placesVisited?: number;
  };
  joinedAt: any;
  lastActive?: any;
}

export const createUserProfile = async (uid: string, userData: Partial<UserProfile>) => {
  try {
    const userRef = doc(db, 'users', uid);
    
    const defaultProfile: UserProfile = {
      displayName: userData.displayName || 'Explorer',
      photoURL: userData.photoURL || '',
      email: userData.email || '',
      role: userData.role || 'explorer',
      city: userData.city || '',
      title: userData.title || 'New Explorer',
      level: userData.level || 1,
      stats: {
        totalBadges: 0,
        questsCompleted: 0,
        hoursExplored: 0,
        placesVisited: 0,
        ...userData.stats,
      },
      joinedAt: serverTimestamp(),
      lastActive: serverTimestamp(),
      ...userData,
    };

    await setDoc(userRef, defaultProfile);
    return defaultProfile;
  } catch (error) {
    console.error('Error creating user profile:', error);
    throw error;
  }
};

export const getUserProfile = async (uid: string): Promise<UserProfile | null> => {
  try {
    const userRef = doc(db, 'users', uid);
    const userSnap = await getDoc(userRef);
    
    if (userSnap.exists()) {
      return userSnap.data() as UserProfile;
    }
    return null;
  } catch (error) {
    console.error('Error getting user profile:', error);
    throw error;
  }
};

export const updateUserProfile = async (uid: string, updates: Partial<UserProfile>) => {
  try {
    const userRef = doc(db, 'users', uid);
    await updateDoc(userRef, {
      ...updates,
      lastActive: serverTimestamp(),
    });
  } catch (error) {
    console.error('Error updating user profile:', error);
    throw error;
  }
};

export const updateUserStats = async (uid: string, statUpdates: Partial<UserProfile['stats']>) => {
  try {
    const userRef = doc(db, 'users', uid);
    const userDoc = await getDoc(userRef);
    
    if (userDoc.exists()) {
      const currentStats = userDoc.data().stats || {};
      const newStats = { ...currentStats, ...statUpdates };
      
      await updateDoc(userRef, {
        stats: newStats,
        lastActive: serverTimestamp(),
      });
    }
  } catch (error) {
    console.error('Error updating user stats:', error);
    throw error;
  }
};

export const incrementUserBadges = async (uid: string, increment: number = 1) => {
  try {
    const userRef = doc(db, 'users', uid);
    const userDoc = await getDoc(userRef);
    
    if (userDoc.exists()) {
      const currentStats = userDoc.data().stats || {};
      const newBadgeCount = (currentStats.totalBadges || 0) + increment;
      
      await updateDoc(userRef, {
        'stats.totalBadges': newBadgeCount,
        lastActive: serverTimestamp(),
      });
    }
  } catch (error) {
    console.error('Error incrementing user badges:', error);
    throw error;
  }
};

export const completeQuest = async (uid: string, questId: string) => {
  try {
    const userRef = doc(db, 'users', uid);
    const userDoc = await getDoc(userRef);
    
    if (userDoc.exists()) {
      const currentStats = userDoc.data().stats || {};
      const newQuestCount = (currentStats.questsCompleted || 0) + 1;
      
      // Calculate level based on quests completed
      const newLevel = Math.floor(newQuestCount / 3) + 1;
      
      await updateDoc(userRef, {
        'stats.questsCompleted': newQuestCount,
        level: newLevel,
        lastActive: serverTimestamp(),
      });
    }
  } catch (error) {
    console.error('Error completing quest:', error);
    throw error;
  }
};