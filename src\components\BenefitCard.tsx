import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { lightTheme } from '../config/theme';

interface BenefitCardProps {
  title: string;
  description: string;
  icon: string;
  color: string;
}

export default function BenefitCard({ title, description, icon, color }: BenefitCardProps) {
  const gradientColors = [
    color,
    color + 'DD', // Slightly transparent version for gradient effect
    color + '99'
  ] as const;

  return (
    <LinearGradient
      colors={gradientColors}
      style={styles.card}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <View style={styles.iconContainer}>
        <Text style={styles.icon}>{icon}</Text>
      </View>
      <Text style={styles.title}>{title}</Text>
      <Text style={styles.description}>{description}</Text>
      <View style={styles.decorativeElement} />
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  card: {
    width: 300,
    height: 220,
    borderRadius: lightTheme.borderRadius.xxl,
    padding: lightTheme.spacing.xl,
    marginHorizontal: lightTheme.spacing.md,
    justifyContent: 'space-between',
    ...lightTheme.shadows.large,
    position: 'relative',
    overflow: 'hidden',
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: lightTheme.spacing.lg,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: lightTheme.borderRadius.round,
    width: 80,
    height: 80,
    justifyContent: 'center',
    alignSelf: 'center',
    ...lightTheme.shadows.medium,
  },
  icon: {
    fontSize: 40,
    color: '#fff',
  },
  title: {
    ...lightTheme.typography.h5,
    color: '#fff',
    textAlign: 'center',
    marginBottom: lightTheme.spacing.sm,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  description: {
    ...lightTheme.typography.body2,
    color: '#fff',
    textAlign: 'center',
    opacity: 0.95,
    lineHeight: 22,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  decorativeElement: {
    position: 'absolute',
    top: -50,
    right: -50,
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    transform: [{ rotate: '45deg' }],
  },
});