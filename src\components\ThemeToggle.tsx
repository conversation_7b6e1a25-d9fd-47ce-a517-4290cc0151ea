import React from 'react';
import {
  TouchableOpacity,
  StyleSheet,
  Animated,
  View,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';

interface ThemeToggleProps {
  size?: number;
  style?: any;
}

export default function ThemeToggle({ size = 24, style }: ThemeToggleProps) {
  const { isDark, toggleTheme, theme } = useTheme();
  const [animatedValue] = React.useState(new Animated.Value(isDark ? 1 : 0));

  React.useEffect(() => {
    Animated.spring(animatedValue, {
      toValue: isDark ? 1 : 0,
      useNativeDriver: false,
      tension: 100,
      friction: 8,
    }).start();
  }, [isDark]);

  const handlePress = () => {
    toggleTheme();
  };

  const iconRotation = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '180deg'],
  });

  const iconScale = animatedValue.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [1, 0.8, 1],
  });

  const backgroundColor = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [theme.colors.primaryAccent + '20', theme.colors.primaryAccent + '30'],
  });

  return (
    <TouchableOpacity onPress={handlePress} style={[styles.container, style]}>
      <Animated.View
        style={[
          styles.toggleBackground,
          {
            backgroundColor,
            width: size * 1.8,
            height: size * 1.8,
            borderRadius: size * 0.9,
          },
        ]}
      >
        <Animated.View
          style={[
            styles.iconContainer,
            {
              transform: [
                { rotate: iconRotation },
                { scale: iconScale },
              ],
            },
          ]}
        >
          <Ionicons
            name={isDark ? 'moon' : 'sunny'}
            size={size}
            color={theme.colors.primaryAccent}
          />
        </Animated.View>
        
        {/* Glow effect */}
        <Animated.View
          style={[
            styles.glowEffect,
            {
              width: size * 2.2,
              height: size * 2.2,
              borderRadius: size * 1.1,
              opacity: animatedValue.interpolate({
                inputRange: [0, 1],
                outputRange: [0.3, 0.6],
              }),
            },
          ]}
        />
      </Animated.View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  toggleBackground: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
  glowEffect: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    backgroundColor: 'rgba(99, 102, 241, 0.2)',
    transform: [{ translateX: -20 }, { translateY: -20 }],
    zIndex: 1,
  },
});