{"name": "mcqs", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "deploy": "git add . && git commit -m \"new fixes\" && git pull && git push", "setup": "git pull && npm install -f && npm run start"}, "dependencies": {"@expo-google-fonts/dm-sans": "^0.4.1", "@expo-google-fonts/inter": "^0.4.1", "@expo-google-fonts/poppins": "^0.4.0", "@expo/vector-icons": "^14.1.0", "@hookform/resolvers": "^5.1.1", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.21", "@reduxjs/toolkit": "^2.8.2", "expo": "~53.0.17", "expo-audio": "^0.4.8", "expo-document-picker": "^13.1.6", "expo-file-system": "^18.1.11", "expo-firebase-analytics": "^8.0.0", "expo-font": "^13.3.2", "expo-google-fonts": "^0.0.0", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "^14.1.5", "expo-location": "^18.1.6", "expo-media-library": "^17.1.7", "expo-status-bar": "~2.2.3", "expo-video": "^2.2.2", "firebase": "^11.10.0", "geolib": "^3.3.4", "react": "19.0.0", "react-hook-form": "^7.60.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-paper": "^5.14.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.11.2", "react-native-webview": "^13.13.5", "react-redux": "^9.2.0", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@react-native-community/eslint-config": "^3.2.0", "@types/react": "~19.0.10", "eslint": "^8.57.1", "prettier": "^3.6.2", "typescript": "~5.8.3"}, "private": true}