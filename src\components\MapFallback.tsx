import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Linking,
} from 'react-native';
import { Card, Button } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { lightTheme } from '../config/theme';

interface MapFallbackProps {
  latitude: number;
  longitude: number;
  onLocationPress?: () => void;
  showOpenMaps?: boolean;
  title?: string;
}

export default function MapFallback({
  latitude,
  longitude,
  onLocationPress,
  showOpenMaps = true,
  title = "Location"
}: MapFallbackProps) {
  const hasValidCoords = latitude !== 0 && longitude !== 0;

  const openInMaps = () => {
    if (hasValidCoords) {
      const url = `https://maps.google.com/?q=${latitude},${longitude}`;
      Linking.openURL(url);
    }
  };

  return (
    <Card style={styles.container}>
      <Card.Content>
        <View style={styles.header}>
          <Ionicons name="map" size={24} color={lightTheme.colors.primaryAccent} />
          <Text style={styles.title}>{title}</Text>
        </View>
        
        {hasValidCoords ? (
          <>
            <View style={styles.coordsContainer}>
              <View style={styles.coordRow}>
                <Text style={styles.coordLabel}>Latitude:</Text>
                <Text style={styles.coordValue}>{latitude.toFixed(6)}</Text>
              </View>
              <View style={styles.coordRow}>
                <Text style={styles.coordLabel}>Longitude:</Text>
                <Text style={styles.coordValue}>{longitude.toFixed(6)}</Text>
              </View>
            </View>
            
            <View style={styles.actions}>
              {showOpenMaps && (
                <Button
                  mode="outlined"
                  onPress={openInMaps}
                  icon="map"
                  style={styles.button}
                >
                  Open in Maps
                </Button>
              )}
              {onLocationPress && (
                <Button
                  mode="contained"
                  onPress={onLocationPress}
                  icon="crosshairs-gps"
                  style={styles.button}
                >
                  Update Location
                </Button>
              )}
            </View>
          </>
        ) : (
          <View style={styles.emptyState}>
            <Ionicons name="location-outline" size={48} color={lightTheme.colors.textSecondary} />
            <Text style={styles.emptyText}>No location selected</Text>
            {onLocationPress && (
              <Button
                mode="contained"
                onPress={onLocationPress}
                icon="add-location"
                style={styles.button}
              >
                Set Location
              </Button>
            )}
          </View>
        )}
      </Card.Content>
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: lightTheme.spacing.md,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: lightTheme.spacing.md,
  },
  title: {
    ...lightTheme.typography.h6,
    color: lightTheme.colors.textPrimary,
    marginLeft: lightTheme.spacing.sm,
  },
  coordsContainer: {
    backgroundColor: lightTheme.colors.surface,
    padding: lightTheme.spacing.md,
    borderRadius: lightTheme.borderRadius.lg,
    marginBottom: lightTheme.spacing.md,
  },
  coordRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: lightTheme.spacing.xs,
  },
  coordLabel: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textSecondary,
  },
  coordValue: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textPrimary,
    fontWeight: '600',
    fontFamily: 'monospace',
  },
  actions: {
    flexDirection: 'row',
    gap: lightTheme.spacing.sm,
  },
  button: {
    flex: 1,
  },
  emptyState: {
    alignItems: 'center',
    padding: lightTheme.spacing.xl,
  },
  emptyText: {
    ...lightTheme.typography.body1,
    color: lightTheme.colors.textSecondary,
    marginVertical: lightTheme.spacing.md,
  },
});