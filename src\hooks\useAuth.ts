import { useSelector, useDispatch } from 'react-redux';
import { RootState, AppDispatch } from '../store/store';
import {
  checkEmailExists,
  checkEmailForRegistration,
  completeRegistration,
  loginWithPassword,
  verifyEmail,
  logout,
  loadUserFromStorage,
  clearError,
  setRegistrationStep,
  clearTempRegistrationData,
} from '../store/authSlice';

export const useAuth = () => {
  const dispatch = useDispatch<AppDispatch>();
  const auth = useSelector((state: RootState) => state.auth);

  return {
    // State
    user: auth.user,
    isAuthenticated: auth.isAuthenticated,
    isLoading: auth.isLoading,
    error: auth.error,
    registrationStep: auth.registrationStep,
    tempRegistrationData: auth.tempRegistrationData,

    // Actions
    checkEmailExists: (email: string) => dispatch(checkEmailExists(email)),
    checkEmailForRegistration: (email: string) => dispatch(checkEmailForRegistration(email)),
    completeRegistration: (userData: {
      email: string;
      username: string;
      fullName: string;
      mobile?: string;
      password: string;
    }) => dispatch(completeRegistration(userData)),
    loginWithPassword: (credentials: { email: string; password: string }) =>
      dispatch(loginWithPassword(credentials)),
    verifyEmail: () => dispatch(verifyEmail()),
    logout: () => dispatch(logout()),
    loadUserFromStorage: () => dispatch(loadUserFromStorage()),
    clearError: () => dispatch(clearError()),
    setRegistrationStep: (step: 'email' | 'details' | 'verification' | 'complete') =>
      dispatch(setRegistrationStep(step)),
    clearTempRegistrationData: () => dispatch(clearTempRegistrationData()),
  };
};