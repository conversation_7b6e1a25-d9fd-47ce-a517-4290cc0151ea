import React from 'react';
import { Text, View, TextStyle, ViewStyle } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { getIconType } from '../utils/iconUtils';

interface IconRendererProps {
  icon: string;
  size?: number;
  color?: string;
  style?: TextStyle | ViewStyle;
  fallbackIcon?: string;
}

export default function IconRenderer({ 
  icon, 
  size = 24, 
  color = '#000', 
  style, 
  fallbackIcon = 'help-outline' 
}: IconRendererProps) {
  const iconType = getIconType(icon);
  
  if (iconType === 'ionicon') {
    return (
      <Ionicons 
        name={icon as any} 
        size={size} 
        color={color} 
        style={style}
      />
    );
  } else if (iconType === 'emoji') {
    return (
      <Text style={[{ fontSize: size, color }, style]}>
        {icon}
      </Text>
    );
  } else {
    // Fallback for unknown icons
    return (
      <Ionicons 
        name={fallbackIcon as any} 
        size={size} 
        color={color} 
        style={style}
      />
    );
  }
}