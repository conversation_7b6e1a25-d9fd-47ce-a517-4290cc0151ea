import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  Dimensions,
} from 'react-native';
import { 
  Button, 
  Card, 
  TextInput,
  Searchbar,
  List,
  Portal,
  Modal,
} from 'react-native-paper';
import { useFormContext, Controller } from 'react-hook-form';
import MapFallback from '../../../components/MapFallback';
import * as Location from 'expo-location';
import { lightTheme } from '../../../config/theme';
import { BadgeFormData } from '../../../services/BadgeService';
import { Ionicons } from '@expo/vector-icons';

const { width, height } = Dimensions.get('window');

// Sample address suggestions (in a real app, you'd use a geocoding service)
const SAMPLE_ADDRESSES = [
  { name: 'India Gate, New Delhi', coords: { lat: 28.6129, lng: 77.2295 } },
  { name: 'Gateway of India, Mumbai', coords: { lat: 18.9220, lng: 72.8347 } },
  { name: 'Bangalore Palace, Bangalore', coords: { lat: 12.9984, lng: 77.5946 } },
  { name: 'Mysore Palace, Mysore', coords: { lat: 12.3051, lng: 76.6551 } },
  { name: 'Hawa Mahal, Jaipur', coords: { lat: 26.9239, lng: 75.8267 } },
  { name: 'Charminar, Hyderabad', coords: { lat: 17.3616, lng: 78.4747 } },
  { name: 'Victoria Memorial, Kolkata', coords: { lat: 22.5448, lng: 88.3426 } },
  { name: 'Meenakshi Temple, Madurai', coords: { lat: 9.9195, lng: 78.1193 } },
  { name: 'Golden Temple, Amritsar', coords: { lat: 31.6200, lng: 74.8765 } },
  { name: 'Lotus Temple, New Delhi', coords: { lat: 28.5535, lng: 77.2588 } },
];

export default function BadgeLocationTab() {
  const { control, watch, setValue, formState: { errors } } = useFormContext<BadgeFormData>();
  const [searchQuery, setSearchQuery] = useState('');
  const [suggestions, setSuggestions] = useState<typeof SAMPLE_ADDRESSES>([]);
  const [addressModalVisible, setAddressModalVisible] = useState(false);
  const [currentLocation, setCurrentLocation] = useState<Location.LocationObject | null>(null);

  const coords = watch('coords');


  useEffect(() => {
    if (searchQuery.length > 2) {
      const filtered = SAMPLE_ADDRESSES.filter(address =>
        address.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setSuggestions(filtered);
    } else {
      setSuggestions([]);
    }
  }, [searchQuery]);

  const requestLocationPermission = async () => {
    const { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission required', 'Please grant location permission to use this feature');
      return false;
    }
    return true;
  };

  const getCurrentLocation = async () => {
    const hasPermission = await requestLocationPermission();
    if (!hasPermission) return;

    try {
      const location = await Location.getCurrentPositionAsync({});
      setCurrentLocation(location);
      
      setValue('coords', {
        lat: location.coords.latitude,
        lng: location.coords.longitude,
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to get current location');
    }
  };


  const handleAddressSelect = (address: typeof SAMPLE_ADDRESSES[0]) => {
    setValue('coords', address.coords);
    setSearchQuery(address.name);
    setAddressModalVisible(false);
  };

  const formatCoordinate = (coord: number) => {
    return coord.toFixed(6);
  };

  return (
    <View style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.sectionTitle}>📍 Pin Location</Text>
          <Text style={styles.sectionSubtitle}>
            Tap on the map to set the exact location for this badge
          </Text>
          
          {/* Search Bar */}
          <View style={styles.searchContainer}>
            <Searchbar
              placeholder="Search for address..."
              onChangeText={setSearchQuery}
              value={searchQuery}
              style={styles.searchBar}
              onFocus={() => setAddressModalVisible(true)}
            />
          </View>

          {/* Location Actions */}
          <View style={styles.locationActions}>
            <Button
              mode="outlined"
              onPress={getCurrentLocation}
              style={styles.locationButton}
              icon="my-location"
            >
              Current Location
            </Button>
            <Button
              mode="outlined"
              onPress={() => setAddressModalVisible(true)}
              style={styles.locationButton}
              icon="magnify"
            >
              Search Address
            </Button>
          </View>

          {/* Location Display */}
          <MapFallback
            latitude={coords.lat}
            longitude={coords.lng}
            title="Badge Location"
            onLocationPress={() => {
              Alert.alert(
                'Set Location',
                'Choose how to set the location:',
                [
                  { text: 'Cancel', style: 'cancel' },
                  { text: 'Use Current Location', onPress: getCurrentLocation },
                  { text: 'Search Address', onPress: () => setAddressModalVisible(true) },
                  { 
                    text: 'Enter Coordinates', 
                    onPress: () => {
                      Alert.alert(
                        'Enter Coordinates',
                        'Please use the address search or current location options for now.'
                      );
                    }
                  }
                ]
              );
            }}
          />

          {/* Coordinates Display */}
          {coords.lat !== 0 && coords.lng !== 0 && (
            <View style={styles.coordinatesContainer}>
              <Text style={styles.coordinatesTitle}>Selected Coordinates:</Text>
              <View style={styles.coordinatesRow}>
                <View style={styles.coordinateItem}>
                  <Text style={styles.coordinateLabel}>Latitude:</Text>
                  <Text style={styles.coordinateValue}>{formatCoordinate(coords.lat)}</Text>
                </View>
                <View style={styles.coordinateItem}>
                  <Text style={styles.coordinateLabel}>Longitude:</Text>
                  <Text style={styles.coordinateValue}>{formatCoordinate(coords.lng)}</Text>
                </View>
              </View>
            </View>
          )}

          {errors.coords && (
            <Text style={styles.errorText}>
              {errors.coords.lat?.message || errors.coords.lng?.message || 'Please select a location'}
            </Text>
          )}
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.sectionTitle}>ℹ️ Location Tips</Text>
          <View style={styles.tipsContainer}>
            <Text style={styles.tipText}>• Be as precise as possible with the location</Text>
            <Text style={styles.tipText}>• Consider accessibility for users</Text>
            <Text style={styles.tipText}>• Check if the location is publicly accessible</Text>
            <Text style={styles.tipText}>• Avoid private property or restricted areas</Text>
            <Text style={styles.tipText}>• Consider safety and lighting for the area</Text>
          </View>
        </Card.Content>
      </Card>

      {/* Address Search Modal */}
      <Portal>
        <Modal
          visible={addressModalVisible}
          onDismiss={() => setAddressModalVisible(false)}
          contentContainerStyle={styles.modalContainer}
        >
          <Card style={styles.modalCard}>
            <Card.Title title="Search Location" />
            <Card.Content>
              <Searchbar
                placeholder="Search for address..."
                onChangeText={setSearchQuery}
                value={searchQuery}
                style={styles.modalSearchBar}
                autoFocus
              />
              <View style={styles.suggestionsContainer}>
                {suggestions.map((address, index) => (
                  <List.Item
                    key={index}
                    title={address.name}
                    left={(props) => (
                      <Ionicons name="location" size={24} color={lightTheme.colors.textSecondary} />
                    )}
                    onPress={() => handleAddressSelect(address)}
                    style={styles.suggestionItem}
                  />
                ))}
                {suggestions.length === 0 && searchQuery.length > 2 && (
                  <Text style={styles.noResultsText}>No results found</Text>
                )}
              </View>
            </Card.Content>
          </Card>
        </Modal>
      </Portal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  card: {
    marginBottom: lightTheme.spacing.lg,
    borderRadius: lightTheme.borderRadius.lg,
  },
  sectionTitle: {
    ...lightTheme.typography.h6,
    color: lightTheme.colors.textPrimary,
    marginBottom: lightTheme.spacing.sm,
  },
  sectionSubtitle: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textSecondary,
    marginBottom: lightTheme.spacing.lg,
  },
  searchContainer: {
    marginBottom: lightTheme.spacing.md,
  },
  searchBar: {
    backgroundColor: lightTheme.colors.surface,
  },
  locationActions: {
    flexDirection: 'row',
    gap: lightTheme.spacing.md,
    marginBottom: lightTheme.spacing.lg,
  },
  locationButton: {
    flex: 1,
  },
  coordinatesContainer: {
    backgroundColor: lightTheme.colors.surface,
    padding: lightTheme.spacing.md,
    borderRadius: lightTheme.borderRadius.lg,
    borderWidth: 1,
    borderColor: lightTheme.colors.borderLight,
  },
  coordinatesTitle: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textPrimary,
    fontWeight: '600',
    marginBottom: lightTheme.spacing.sm,
  },
  coordinatesRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  coordinateItem: {
    flex: 1,
  },
  coordinateLabel: {
    ...lightTheme.typography.caption,
    color: lightTheme.colors.textSecondary,
  },
  coordinateValue: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textPrimary,
    fontWeight: '600',
    fontFamily: 'monospace',
  },
  tipsContainer: {
    gap: lightTheme.spacing.sm,
  },
  tipText: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textSecondary,
  },
  modalContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalCard: {
    width: '90%',
    maxHeight: '70%',
    borderRadius: lightTheme.borderRadius.lg,
  },
  modalSearchBar: {
    marginBottom: lightTheme.spacing.md,
  },
  suggestionsContainer: {
    maxHeight: 300,
  },
  suggestionItem: {
    borderBottomWidth: 1,
    borderBottomColor: lightTheme.colors.borderLight,
  },
  noResultsText: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textSecondary,
    textAlign: 'center',
    padding: lightTheme.spacing.lg,
  },
  errorText: {
    ...lightTheme.typography.caption,
    color: lightTheme.colors.error,
    marginTop: lightTheme.spacing.sm,
  },
});