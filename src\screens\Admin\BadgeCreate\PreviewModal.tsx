import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  Dimensions,
} from 'react-native';
import { 
  <PERSON>dal, 
  Card, 
  Button, 
  Chip,
  Portal,
  Divider,
} from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { lightTheme } from '../../../config/theme';
import { BadgeFormData, BADGE_CATEGORIES } from '../../../services/BadgeService';
import { Ionicons } from '@expo/vector-icons';
import IconRenderer from '../../../components/IconRenderer';

const { width } = Dimensions.get('window');

interface BadgePreviewProps {
  visible: boolean;
  onDismiss: () => void;
  badgeData: BadgeFormData;
  selectedImage: any;
}

export default function BadgePreview({ visible, onDismiss, badgeData, selectedImage }: BadgePreviewProps) {
  const getCategoryInfo = (categoryId: string) => {
    return BADGE_CATEGORIES.find(cat => cat.id === categoryId);
  };

  const categoryInfo = getCategoryInfo(badgeData.category);

  const formatCoordinate = (coord: number) => {
    return coord.toFixed(6);
  };

  const getUnlockMethodText = (method: string) => {
    return method === 'gps' ? 'GPS Location' : 'Secret Code';
  };

  const getUnlockMethodIcon = (method: string) => {
    return method === 'gps' ? 'location' : 'lock-closed';
  };

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={styles.modalContainer}
      >
        <Card style={styles.modalCard}>
          <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
            {/* Header */}
            <LinearGradient
              colors={lightTheme.gradients.primary as any}
              style={styles.header}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Text style={styles.headerTitle}>Badge Preview</Text>
              <Text style={styles.headerSubtitle}>How your badge will appear to users</Text>
            </LinearGradient>

            {/* Badge Card Preview */}
            <Card style={styles.badgeCard}>
              <Card.Content>
                <View style={styles.badgeHeader}>
                  <View style={styles.badgeIconContainer}>
                    {selectedImage ? (
                      <Image source={{ uri: selectedImage.uri }} style={styles.badgeImage} />
                    ) : (
                      <View style={styles.badgeIconBackground}>
                        <IconRenderer 
                          icon={badgeData.icon} 
                          size={32} 
                          color={lightTheme.colors.primaryAccent} 
                        />
                      </View>
                    )}
                  </View>
                  <View style={styles.badgeInfo}>
                    <Text style={styles.badgeTitle}>{badgeData.title || 'Badge Title'}</Text>
                    <View style={styles.badgeMetadata}>
                      {categoryInfo && (
                        <Chip
                          style={styles.categoryChip}
                          textStyle={styles.categoryChipText}
                          icon={categoryInfo.icon}
                          compact
                        >
                          {categoryInfo.name}
                        </Chip>
                      )}
                      <Chip
                        style={styles.unlockChip}
                        textStyle={styles.unlockChipText}
                        icon={getUnlockMethodIcon(badgeData.unlockMethod)}
                        compact
                      >
                        {getUnlockMethodText(badgeData.unlockMethod)}
                      </Chip>
                    </View>
                  </View>
                </View>

                <Divider style={styles.divider} />

                {/* Clue Section */}
                <View style={styles.section}>
                  <Text style={styles.sectionTitle}>🔍 Clue</Text>
                  <Text style={styles.sectionContent}>
                    {badgeData.clue || 'Your clue will appear here...'}
                  </Text>
                </View>

                <Divider style={styles.divider} />

                {/* Story Section */}
                <View style={styles.section}>
                  <Text style={styles.sectionTitle}>📖 Story</Text>
                  <Text style={styles.sectionContent}>
                    {badgeData.story || 'Your story will appear here...'}
                  </Text>
                </View>

                {/* Unlock Method Details */}
                {badgeData.unlockMethod === 'code' && badgeData.code && (
                  <>
                    <Divider style={styles.divider} />
                    <View style={styles.section}>
                      <Text style={styles.sectionTitle}>🔐 Secret Code</Text>
                      <View style={styles.codeContainer}>
                        <Text style={styles.codeText}>{badgeData.code}</Text>
                      </View>
                    </View>
                  </>
                )}
              </Card.Content>
            </Card>

            {/* Location Details */}
            <Card style={styles.detailsCard}>
              <Card.Content>
                <Text style={styles.detailsTitle}>📍 Location Details</Text>
                
                <View style={styles.detailsRow}>
                  <Text style={styles.detailLabel}>State:</Text>
                  <Text style={styles.detailValue}>{badgeData.state || 'Not specified'}</Text>
                </View>

                <View style={styles.detailsRow}>
                  <Text style={styles.detailLabel}>Cities:</Text>
                  <Text style={styles.detailValue}>
                    {badgeData.city?.length > 0 ? badgeData.city.join(', ') : 'None selected'}
                  </Text>
                </View>

                {badgeData.coords.lat !== 0 && badgeData.coords.lng !== 0 && (
                  <View style={styles.detailsRow}>
                    <Text style={styles.detailLabel}>Coordinates:</Text>
                    <Text style={styles.detailValue}>
                      {formatCoordinate(badgeData.coords.lat)}, {formatCoordinate(badgeData.coords.lng)}
                    </Text>
                  </View>
                )}
              </Card.Content>
            </Card>

            {/* Action Buttons */}
            <View style={styles.actionButtons}>
              <Button
                mode="outlined"
                onPress={onDismiss}
                style={styles.actionButton}
              >
                Close Preview
              </Button>
            </View>
          </ScrollView>
        </Card>
      </Modal>
    </Portal>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalCard: {
    width: width * 0.9,
    maxHeight: '90%',
    borderRadius: lightTheme.borderRadius.lg,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingVertical: lightTheme.spacing.xl,
    paddingHorizontal: lightTheme.spacing.lg,
    alignItems: 'center',
    borderTopLeftRadius: lightTheme.borderRadius.lg,
    borderTopRightRadius: lightTheme.borderRadius.lg,
  },
  headerTitle: {
    ...lightTheme.typography.h5,
    color: lightTheme.colors.white,
    marginBottom: lightTheme.spacing.sm,
  },
  headerSubtitle: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.white,
    opacity: 0.9,
    textAlign: 'center',
  },
  badgeCard: {
    margin: lightTheme.spacing.lg,
    borderRadius: lightTheme.borderRadius.lg,
    elevation: 4,
  },
  badgeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: lightTheme.spacing.lg,
  },
  badgeIconContainer: {
    marginRight: lightTheme.spacing.md,
  },
  badgeImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  badgeIconBackground: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: lightTheme.colors.primaryAccent + '20',
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeInfo: {
    flex: 1,
  },
  badgeTitle: {
    ...lightTheme.typography.h6,
    color: lightTheme.colors.textPrimary,
    marginBottom: lightTheme.spacing.sm,
  },
  badgeMetadata: {
    flexDirection: 'row',
    gap: lightTheme.spacing.sm,
    flexWrap: 'wrap',
  },
  categoryChip: {
    backgroundColor: lightTheme.colors.primaryAccent + '20',
    height: 28,
  },
  categoryChipText: {
    color: lightTheme.colors.primaryAccent,
    fontSize: 12,
  },
  unlockChip: {
    backgroundColor: lightTheme.colors.success + '20',
    height: 28,
  },
  unlockChipText: {
    color: lightTheme.colors.success,
    fontSize: 12,
  },
  divider: {
    marginVertical: lightTheme.spacing.lg,
  },
  section: {
    marginBottom: lightTheme.spacing.md,
  },
  sectionTitle: {
    ...lightTheme.typography.h6,
    color: lightTheme.colors.textPrimary,
    marginBottom: lightTheme.spacing.sm,
  },
  sectionContent: {
    ...lightTheme.typography.body1,
    color: lightTheme.colors.textSecondary,
    lineHeight: 22,
  },
  codeContainer: {
    backgroundColor: lightTheme.colors.surface,
    padding: lightTheme.spacing.md,
    borderRadius: lightTheme.borderRadius.md,
    borderWidth: 1,
    borderColor: lightTheme.colors.borderLight,
    alignItems: 'center',
  },
  codeText: {
    ...lightTheme.typography.h5,
    color: lightTheme.colors.textPrimary,
    fontFamily: 'monospace',
    fontWeight: '700',
    letterSpacing: 2,
  },
  detailsCard: {
    margin: lightTheme.spacing.lg,
    marginTop: 0,
    borderRadius: lightTheme.borderRadius.lg,
  },
  detailsTitle: {
    ...lightTheme.typography.h6,
    color: lightTheme.colors.textPrimary,
    marginBottom: lightTheme.spacing.lg,
  },
  detailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: lightTheme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: lightTheme.colors.borderLight,
  },
  detailLabel: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textSecondary,
    fontWeight: '600',
  },
  detailValue: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textPrimary,
    flex: 1,
    textAlign: 'right',
  },
  actionButtons: {
    padding: lightTheme.spacing.lg,
  },
  actionButton: {
    width: '100%',
  },
});