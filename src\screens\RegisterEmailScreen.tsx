import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Dimensions,
} from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../App';
import { lightTheme } from '../config/theme';
import { useAuth } from '../hooks/useAuth';

type RegisterEmailScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'RegisterEmail'>;

export default function RegisterEmailScreen() {
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState('');
  const navigation = useNavigation<RegisterEmailScreenNavigationProp>();
  const { checkEmailForRegistration, isLoading, error, clearError } = useAuth();
  const insets = useSafeAreaInsets();

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleEmailChange = (text: string) => {
    setEmail(text);
    setEmailError('');
    if (error) clearError();
  };

  const handleContinue = async () => {
    if (!email.trim()) {
      setEmailError('Please enter your email address');
      return;
    }

    if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address');
      return;
    }

    try {
      const result = await checkEmailForRegistration(email);
      if (result.meta.requestStatus === 'fulfilled') {
        navigation.navigate('RegisterProfile', { 
          email: (result.payload as { email: string }).email
        });
      }
    } catch (err) {
      // Error is handled by Redux
    }
  };

  const handleBackToLogin = () => {
    navigation.navigate('Landing');
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={[
            styles.scrollContent,
            { paddingBottom: insets.bottom + 20 }
          ]}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Header Section */}
          <View style={styles.header}>
            <View style={styles.heroIcon}>
              <Text style={styles.heroIconText}>🚀</Text>
            </View>
            <Text style={styles.title}>Create Account</Text>
            <Text style={styles.subtitle}>
              Join thousands of explorers and discover hidden gems in your city
            </Text>
          </View>

          {/* Form Section */}
          <View style={styles.form}>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Email Address</Text>
              <View style={styles.inputWrapper}>
                <TextInput
                  style={[
                    styles.input,
                    (emailError || error) && styles.inputError
                  ]}
                  placeholder="Enter your email"
                  placeholderTextColor={lightTheme.colors.textSecondary}
                  value={email}
                  onChangeText={handleEmailChange}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                  editable={!isLoading}
                  returnKeyType="next"
                  onSubmitEditing={handleContinue}
                />
                <View style={styles.inputIcon}>
                  <Text style={styles.inputIconText}>📧</Text>
                </View>
              </View>
              {(emailError || error) && (
                <Text style={styles.errorText}>
                  {emailError || error}
                </Text>
              )}
            </View>

            <TouchableOpacity
              style={[
                styles.continueButtonContainer,
                isLoading && styles.continueButtonDisabled
              ]}
              onPress={handleContinue}
              disabled={isLoading}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={isLoading ? 
                  [lightTheme.colors.textSecondary, lightTheme.colors.textSecondary] : 
                  lightTheme.gradients.primary
                }
                style={styles.continueButton}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                <Text style={styles.continueButtonText}>
                  {isLoading ? 'Creating Account...' : 'Continue'}
                </Text>
              </LinearGradient>
            </TouchableOpacity>

            {/* Info Box */}
            <View style={styles.infoContainer}>
              <View style={styles.infoBox}>
                <Text style={styles.infoText}>
                  We'll send you a verification code to secure your account
                </Text>
              </View>
            </View>
          </View>
        </ScrollView>

        {/* Footer - Fixed at bottom */}
        <View style={[styles.footer, { paddingBottom: insets.bottom + 16 }]}>
          <TouchableOpacity onPress={handleBackToLogin} activeOpacity={0.7}>
            <Text style={styles.backToLoginText}>
              Already have an account? <Text style={styles.backToLoginLink}>Sign In</Text>
            </Text>
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: lightTheme.colors.background,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: lightTheme.spacing.lg,
  },
  header: {
    alignItems: 'center',
    paddingTop: lightTheme.spacing.xl,
    paddingBottom: lightTheme.spacing.lg,
  },
  heroIcon: {
    backgroundColor: lightTheme.colors.primaryAccent + '20',
    borderRadius: lightTheme.borderRadius.round,
    width: 80,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: lightTheme.spacing.lg,
    ...lightTheme.shadows.medium,
  },
  heroIconText: {
    fontSize: 40,
  },
  title: {
    ...lightTheme.typography.h1,
    color: lightTheme.colors.textPrimary,
    textAlign: 'center',
    marginBottom: lightTheme.spacing.md,
    fontWeight: '700',
  },
  subtitle: {
    ...lightTheme.typography.body1,
    color: lightTheme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: lightTheme.spacing.md,
  },
  form: {
    flex: 1,
    paddingTop: lightTheme.spacing.xl,
  },
  inputContainer: {
    marginBottom: lightTheme.spacing.xl,
  },
  inputLabel: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textPrimary,
    marginBottom: lightTheme.spacing.sm,
    fontWeight: '600',
    marginLeft: lightTheme.spacing.sm,
  },
  inputWrapper: {
    position: 'relative',
  },
  input: {
    backgroundColor: lightTheme.colors.surface,
    borderRadius: lightTheme.borderRadius.lg,
    paddingHorizontal: lightTheme.spacing.lg,
    paddingVertical: lightTheme.spacing.lg,
    paddingRight: 50,
    fontSize: 16,
    color: lightTheme.colors.textPrimary,
    borderWidth: 2,
    borderColor: lightTheme.colors.borderLight,
    minHeight: 56,
    ...lightTheme.shadows.small,
  },
  inputIcon: {
    position: 'absolute',
    right: lightTheme.spacing.lg,
    top: '50%',
    transform: [{ translateY: -12 }],
  },
  inputIconText: {
    fontSize: 20,
    opacity: 0.7,
  },
  inputError: {
    borderColor: lightTheme.colors.error,
    backgroundColor: lightTheme.colors.error + '10',
  },
  errorText: {
    ...lightTheme.typography.caption,
    color: lightTheme.colors.error,
    marginTop: lightTheme.spacing.sm,
    marginLeft: lightTheme.spacing.md,
  },
  continueButtonContainer: {
    borderRadius: lightTheme.borderRadius.lg,
    overflow: 'hidden',
    marginBottom: lightTheme.spacing.lg,
    ...lightTheme.shadows.medium,
  },
  continueButtonDisabled: {
    opacity: 0.6,
  },
  continueButton: {
    paddingVertical: lightTheme.spacing.lg,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 56,
  },
  continueButtonText: {
    ...lightTheme.typography.button,
    color: lightTheme.colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  infoContainer: {
    alignItems: 'center',
    paddingHorizontal: lightTheme.spacing.sm,
  },
  infoBox: {
    backgroundColor: lightTheme.colors.info + '10',
    paddingHorizontal: lightTheme.spacing.lg,
    paddingVertical: lightTheme.spacing.md,
    borderRadius: lightTheme.borderRadius.lg,
    borderLeftWidth: 4,
    borderLeftColor: lightTheme.colors.info,
    width: '100%',
  },
  infoText: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.info,
    textAlign: 'center',
    lineHeight: 20,
  },
  footer: {
    alignItems: 'center',
    paddingTop: lightTheme.spacing.md,
    paddingHorizontal: lightTheme.spacing.lg,
    backgroundColor: lightTheme.colors.background,
    borderTopWidth: 1,
    borderTopColor: lightTheme.colors.borderLight,
  },
  backToLoginText: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textSecondary,
    textAlign: 'center',
  },
  backToLoginLink: {
    color: lightTheme.colors.primaryAccent,
    fontWeight: '600',
  },
});