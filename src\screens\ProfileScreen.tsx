import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  TextInput,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../App';
import { lightTheme, levelBadges } from '../config/theme';
import { useTheme } from '../contexts/ThemeContext';
import ThemeToggle from '../components/ThemeToggle';
import { Card } from 'react-native-paper';
import { trackScreenView } from '../services/analyticsService';
import { useAuth } from '../hooks/useAuth';
import { Ionicons } from '@expo/vector-icons';

type ProfileScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Profile'>;

export default function ProfileScreen() {
  const navigation = useNavigation<ProfileScreenNavigationProp>();
  const [isEditing, setIsEditing] = useState(false);
  const { user, logout: authLogout, isLoading } = useAuth();
  const { theme } = useTheme();
  
  const [editForm, setEditForm] = useState({
    fullName: user?.fullName || '',
    username: user?.username || '',
    mobile: user?.mobile || '',
  });

  useEffect(() => {
    trackScreenView('Profile');
    if (user) {
      setEditForm({
        fullName: user.fullName,
        username: user.username,
        mobile: user.mobile || '',
      });
    }
  }, [user]);

  const handleEditToggle = () => {
    if (isEditing) {
      // TODO: Implement profile update API call
      Alert.alert('Profile Updated', 'Your profile has been updated successfully!');
    }
    setIsEditing(!isEditing);
  };

  const handleLogout = () => {
    Alert.alert(
      '🚪 Logout',
      'Are you sure you want to end your current adventure?',
      [
        { text: 'Stay', style: 'cancel' },
        { 
          text: 'Logout', 
          style: 'destructive',
          onPress: async () => {
            try {
              await authLogout();
              navigation.navigate('Landing');
            } catch (error) {
              Alert.alert('Error', 'Failed to logout. Please try again.');
            }
          }
        },
      ]
    );
  };

  const getLevelProgress = () => {
    if (!user) return 0;
    const currentLevelXP = (user.level - 1) * 500;
    const nextLevelXP = user.level * 500;
    const progress = ((user.xp - currentLevelXP) / (nextLevelXP - currentLevelXP)) * 100;
    return Math.min(progress, 100);
  };

  const getNextLevelXP = () => {
    if (!user) return 0;
    const nextLevelXP = user.level * 500;
    return nextLevelXP - user.xp;
  };

  const currentLevelBadge = levelBadges[user?.level || 1] || levelBadges[1];
  const nextLevelBadge = levelBadges[(user?.level || 1) + 1] || levelBadges[8];

  if (!user) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading profile...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <LinearGradient
            colors={theme.gradients.primary as any}
            style={styles.header}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <View style={styles.avatarContainer}>
              <LinearGradient
                colors={[currentLevelBadge.color, currentLevelBadge.color + '80'] as any}
                style={styles.avatar}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <Text style={styles.avatarText}>{currentLevelBadge.icon}</Text>
              </LinearGradient>
              <View style={styles.levelBadge}>
                <Text style={styles.levelText}>{user.level}</Text>
              </View>
            </View>
            
            {isEditing ? (
              <View style={styles.editingContainer}>
                <TextInput
                  style={styles.nameInput}
                  value={editForm.fullName}
                  onChangeText={(text) => setEditForm({...editForm, fullName: text})}
                  placeholder="Your Full Name"
                  placeholderTextColor={theme.colors.textSecondary}
                />
                <TextInput
                  style={styles.usernameInput}
                  value={editForm.username}
                  onChangeText={(text) => setEditForm({...editForm, username: text})}
                  placeholder="Username"
                  placeholderTextColor={theme.colors.textSecondary}
                />
                <TextInput
                  style={styles.mobileInput}
                  value={editForm.mobile}
                  onChangeText={(text) => setEditForm({...editForm, mobile: text})}
                  placeholder="Mobile Number"
                  placeholderTextColor={theme.colors.textSecondary}
                  keyboardType="phone-pad"
                />
              </View>
            ) : (
              <View style={styles.userInfo}>
                <Text style={styles.userName}>{user.fullName}</Text>
                <Text style={styles.userEmail}>{user.email}</Text>
                <Text style={styles.username}>@{user.username}</Text>
                <Text style={styles.userTitle}>{currentLevelBadge.name}</Text>
              </View>
            )}
            
            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={styles.editButtonContainer}
                onPress={handleEditToggle}
              >
                <LinearGradient
                  colors={['rgba(255,255,255,0.2)', 'rgba(255,255,255,0.1)'] as any}
                  style={styles.editButton}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                >
                  <Ionicons 
                    name={isEditing ? 'checkmark' : 'pencil'} 
                    size={20} 
                    color={lightTheme.colors.white} 
                  />
                  <Text style={styles.editButtonText}>
                    {isEditing ? 'Save' : 'Edit Profile'}
                  </Text>
                </LinearGradient>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.logoutButtonContainer}
                onPress={handleLogout}
                disabled={isLoading}
              >
                <LinearGradient
                  colors={[lightTheme.colors.error, lightTheme.colors.error + '80'] as any}
                  style={styles.logoutButton}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                >
                  <Ionicons name="log-out" size={20} color={lightTheme.colors.white} />
                  <Text style={styles.logoutButtonText}>
                    {isLoading ? 'Logging out...' : 'Logout'}
                  </Text>
                </LinearGradient>
              </TouchableOpacity>
            </View>
            
            {/* Theme Toggle */}
            <View style={styles.themeToggleContainer}>
              <ThemeToggle size={28} />
            </View>
          </LinearGradient>

          <LinearGradient
            colors={[theme.colors.surface, theme.colors.surfaceElevated] as any}
            style={styles.statsContainer}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
          >
            <View style={styles.statsRow}>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{user.xp}</Text>
                <Text style={styles.statLabel}>XP</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{user.questsCompleted}</Text>
                <Text style={styles.statLabel}>Completed</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{user.badges.length}</Text>
                <Text style={styles.statLabel}>Badges</Text>
              </View>
            </View>
            
            <View style={styles.progressContainer}>
              <View style={styles.progressHeader}>
                <Text style={styles.progressLabel}>Level {user.level} Progress</Text>
                <Text style={styles.progressXP}>{getNextLevelXP()} XP to {nextLevelBadge.name}</Text>
              </View>
              <View style={styles.progressBar}>
                <LinearGradient
                  colors={theme.gradients.primary as any}
                  style={[styles.progressFill, { width: `${getLevelProgress()}%` }]}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                />
              </View>
            </View>
          </LinearGradient>

          <LinearGradient
            colors={[theme.colors.surface, theme.colors.surfaceElevated] as any}
            style={styles.infoCard}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
          >
            <Text style={styles.infoTitle}>🎯 Explorer Status</Text>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Current Level:</Text>
              <Text style={styles.infoValue}>{user.level} - {currentLevelBadge.name}</Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Member since:</Text>
              <Text style={styles.infoValue}>{new Date(user.createdAt).toLocaleDateString()}</Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Email verified:</Text>
              <Text style={[styles.infoValue, { color: user.emailVerified ? theme.colors.success : theme.colors.warning }]}>
                {user.emailVerified ? '✅ Verified' : '⚠️ Pending'}
              </Text>
            </View>
            {user.mobile && (
              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>Mobile:</Text>
                <Text style={styles.infoValue}>{user.mobile}</Text>
              </View>
            )}
          </LinearGradient>

          {!user.emailVerified && (
            <LinearGradient
              colors={[theme.colors.warning + '20', theme.colors.warning + '10'] as any}
              style={styles.warningCard}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <Text style={styles.warningTitle}>⚠️ Email Verification Required</Text>
              <Text style={styles.warningText}>
                Please verify your email to access all features and start your quests!
              </Text>
              <TouchableOpacity
                style={styles.verifyButton}
                onPress={() => navigation.navigate('EmailVerification', { email: user.email })}
              >
                <Text style={styles.verifyButtonText}>Verify Email</Text>
              </TouchableOpacity>
            </LinearGradient>
          )}
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: lightTheme.colors.background,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    ...lightTheme.typography.h6,
    color: lightTheme.colors.textSecondary,
  },
  header: {
    alignItems: 'center',
    paddingVertical: lightTheme.spacing.xxl,
    paddingHorizontal: lightTheme.spacing.lg,
    borderBottomLeftRadius: lightTheme.borderRadius.xxxl,
    borderBottomRightRadius: lightTheme.borderRadius.xxxl,
    ...lightTheme.shadows.large,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: lightTheme.spacing.lg,
  },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    ...lightTheme.shadows.medium,
  },
  avatarText: {
    fontSize: 48,
    color: lightTheme.colors.white,
  },
  levelBadge: {
    position: 'absolute',
    bottom: -5,
    right: -5,
    backgroundColor: lightTheme.colors.secondaryAccent,
    borderRadius: lightTheme.borderRadius.round,
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: lightTheme.colors.white,
    ...lightTheme.shadows.medium,
  },
  levelText: {
    ...lightTheme.typography.buttonSmall,
    color: lightTheme.colors.white,
    fontWeight: '800',
  },
  editingContainer: {
    width: '100%',
    alignItems: 'center',
    gap: lightTheme.spacing.md,
  },
  nameInput: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: lightTheme.borderRadius.lg,
    paddingHorizontal: lightTheme.spacing.lg,
    paddingVertical: lightTheme.spacing.md,
    fontSize: 18,
    color: lightTheme.colors.white,
    textAlign: 'center',
    width: '100%',
  },
  usernameInput: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: lightTheme.borderRadius.lg,
    paddingHorizontal: lightTheme.spacing.lg,
    paddingVertical: lightTheme.spacing.md,
    fontSize: 16,
    color: lightTheme.colors.white,
    textAlign: 'center',
    width: '100%',
  },
  mobileInput: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: lightTheme.borderRadius.lg,
    paddingHorizontal: lightTheme.spacing.lg,
    paddingVertical: lightTheme.spacing.md,
    fontSize: 16,
    color: lightTheme.colors.white,
    textAlign: 'center',
    width: '100%',
  },
  userInfo: {
    alignItems: 'center',
    marginBottom: lightTheme.spacing.lg,
  },
  userName: {
    ...lightTheme.typography.h3,
    color: lightTheme.colors.white,
    textAlign: 'center',
    marginBottom: lightTheme.spacing.xs,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  userEmail: {
    ...lightTheme.typography.body1,
    color: lightTheme.colors.white,
    opacity: 0.9,
    textAlign: 'center',
    marginBottom: lightTheme.spacing.xs,
  },
  username: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.white,
    opacity: 0.8,
    textAlign: 'center',
    marginBottom: lightTheme.spacing.sm,
  },
  userTitle: {
    ...lightTheme.typography.h6,
    color: lightTheme.colors.white,
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: lightTheme.spacing.lg,
    paddingVertical: lightTheme.spacing.sm,
    borderRadius: lightTheme.borderRadius.lg,
    textAlign: 'center',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: lightTheme.spacing.md,
    marginTop: lightTheme.spacing.lg,
  },
  editButtonContainer: {
    flex: 1,
    borderRadius: lightTheme.borderRadius.lg,
    overflow: 'hidden',
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: lightTheme.spacing.lg,
    paddingVertical: lightTheme.spacing.md,
    gap: lightTheme.spacing.sm,
  },
  editButtonText: {
    ...lightTheme.typography.buttonSmall,
    color: lightTheme.colors.white,
    fontWeight: '700',
  },
  logoutButtonContainer: {
    flex: 1,
    borderRadius: lightTheme.borderRadius.lg,
    overflow: 'hidden',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: lightTheme.spacing.lg,
    paddingVertical: lightTheme.spacing.md,
    gap: lightTheme.spacing.sm,
  },
  logoutButtonText: {
    ...lightTheme.typography.buttonSmall,
    color: lightTheme.colors.white,
    fontWeight: '700',
  },
  statsContainer: {
    paddingVertical: lightTheme.spacing.xl,
    paddingHorizontal: lightTheme.spacing.lg,
    marginVertical: lightTheme.spacing.lg,
    marginHorizontal: lightTheme.spacing.lg,
    borderRadius: lightTheme.borderRadius.xxl,
    ...lightTheme.shadows.large,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: lightTheme.spacing.lg,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    ...lightTheme.typography.h3,
    color: lightTheme.colors.primaryAccent,
    fontWeight: '800',
  },
  statLabel: {
    ...lightTheme.typography.caption,
    color: lightTheme.colors.textSecondary,
    textTransform: 'uppercase',
    marginTop: lightTheme.spacing.xs,
  },
  progressContainer: {
    width: '100%',
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: lightTheme.spacing.md,
  },
  progressLabel: {
    ...lightTheme.typography.h6,
    color: lightTheme.colors.textPrimary,
    fontWeight: '700',
  },
  progressXP: {
    ...lightTheme.typography.caption,
    color: lightTheme.colors.textSecondary,
    fontWeight: '600',
  },
  progressBar: {
    height: 8,
    backgroundColor: lightTheme.colors.borderLight,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  infoCard: {
    marginVertical: lightTheme.spacing.lg,
    marginHorizontal: lightTheme.spacing.lg,
    paddingVertical: lightTheme.spacing.xl,
    paddingHorizontal: lightTheme.spacing.lg,
    borderRadius: lightTheme.borderRadius.xxl,
    ...lightTheme.shadows.large,
  },
  infoTitle: {
    ...lightTheme.typography.h5,
    color: lightTheme.colors.textPrimary,
    marginBottom: lightTheme.spacing.lg,
    fontWeight: '700',
  },
  infoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: lightTheme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: lightTheme.colors.borderLight,
  },
  infoLabel: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textSecondary,
    fontWeight: '600',
  },
  infoValue: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textPrimary,
    fontWeight: '700',
  },
  warningCard: {
    marginVertical: lightTheme.spacing.lg,
    marginHorizontal: lightTheme.spacing.lg,
    paddingVertical: lightTheme.spacing.xl,
    paddingHorizontal: lightTheme.spacing.lg,
    borderRadius: lightTheme.borderRadius.xxl,
    ...lightTheme.shadows.large,
  },
  warningTitle: {
    ...lightTheme.typography.h6,
    color: lightTheme.colors.warning,
    marginBottom: lightTheme.spacing.md,
    fontWeight: '700',
  },
  warningText: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.warning,
    marginBottom: lightTheme.spacing.lg,
    lineHeight: 20,
  },
  verifyButton: {
    backgroundColor: lightTheme.colors.warning,
    paddingHorizontal: lightTheme.spacing.lg,
    paddingVertical: lightTheme.spacing.md,
    borderRadius: lightTheme.borderRadius.lg,
    alignItems: 'center',
  },
  verifyButtonText: {
    ...lightTheme.typography.buttonSmall,
    color: lightTheme.colors.white,
    fontWeight: '700',
  },
  themeToggleContainer: {
    alignItems: 'center',
    marginTop: lightTheme.spacing.lg,
    paddingVertical: lightTheme.spacing.md,
  },
});