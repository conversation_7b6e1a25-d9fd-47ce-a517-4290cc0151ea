import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../App';
import { lightTheme } from '../config/theme';
import { useAuth } from '../hooks/useAuth';
import { sendEmailVerification } from 'firebase/auth';
import { auth } from '../config/firebase';

type EmailVerificationScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'EmailVerification'>;

interface RouteParams {
  email: string;
}

export default function EmailVerificationScreen() {
  const [countdown, setCountdown] = useState(60);
  const [canResend, setCanResend] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [isCheckingVerification, setIsCheckingVerification] = useState(false);

  const navigation = useNavigation<EmailVerificationScreenNavigationProp>();
  const route = useRoute();
  const { email } = route.params as RouteParams;
  const { verifyEmail, isLoading, error, clearError } = useAuth();

  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [countdown]);


  const handleCheckVerification = async (showLoading = true) => {
    if (showLoading) setIsCheckingVerification(true);
    
    try {
      const result = await verifyEmail();
      if (result.meta.requestStatus === 'fulfilled') {
        Alert.alert(
          '🎉 Email Verified!',
          'Welcome to MyCityQuest! Your adventure awaits.',
          [
            {
              text: 'Start Exploring',
              onPress: () => navigation.navigate('Home'),
            },
          ]
        );
      }
    } catch (err) {
      if (showLoading && error) {
        // Only show error if it's a manual check
        Alert.alert('Verification Check', error);
      }
    } finally {
      if (showLoading) setIsCheckingVerification(false);
    }
  };

  const handleResendEmail = async () => {
    if (!canResend || isResending) return;

    setIsResending(true);
    
    try {
      if (auth.currentUser) {
        await sendEmailVerification(auth.currentUser);
        Alert.alert(
          '📧 Email Sent!',
          'We\'ve sent another verification link to your email.',
          [{ text: 'OK' }]
        );
        setCountdown(60);
        setCanResend(false);
      }
    } catch (error: any) {
      Alert.alert(
        'Resend Failed',
        'Failed to resend verification email. Please try again later.'
      );
    } finally {
      setIsResending(false);
    }
  };

  const handleBackToLogin = () => {
    navigation.navigate('Login');
  };

  const handleChangeEmail = () => {
    Alert.alert(
      'Change Email',
      'To change your email, you\'ll need to start the registration process again.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Start Over',
          onPress: () => navigation.navigate('RegisterEmail'),
        },
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <LinearGradient
          colors={[lightTheme.colors.surface, lightTheme.colors.surfaceElevated] as any}
          style={styles.header}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
        >
          <View style={styles.heroIcon}>
            <Text style={styles.heroIconText}>📧</Text>
          </View>
          <Text style={styles.title}>Check Your Email!</Text>
          <Text style={styles.subtitle}>
            We've sent a verification link to
          </Text>
          <Text style={styles.emailText}>{email}</Text>
        </LinearGradient>

        <View style={styles.instructionsContainer}>
          <LinearGradient
            colors={[lightTheme.colors.info + '20', lightTheme.colors.info + '10'] as any}
            style={styles.instructionsBox}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          >
            <View style={styles.instructionStep}>
              <Text style={styles.stepNumber}>1</Text>
              <Text style={styles.stepText}>Check your inbox (and spam folder)</Text>
            </View>
            <View style={styles.instructionStep}>
              <Text style={styles.stepNumber}>2</Text>
              <Text style={styles.stepText}>Click the verification link</Text>
            </View>
            <View style={styles.instructionStep}>
              <Text style={styles.stepNumber}>3</Text>
              <Text style={styles.stepText}>Return here and we'll detect it automatically!</Text>
            </View>
          </LinearGradient>
        </View>

        <TouchableOpacity
          style={styles.checkButtonContainer}
          onPress={() => handleCheckVerification(true)}
          disabled={isCheckingVerification || isLoading}
        >
          <LinearGradient
            colors={lightTheme.gradients.primary as any}
            style={styles.checkButton}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          >
            <Text style={styles.checkButtonText}>
              {isCheckingVerification ? '🔄 Checking...' : '✅ I\'ve Verified My Email'}
            </Text>
          </LinearGradient>
        </TouchableOpacity>

        <View style={styles.resendContainer}>
          <Text style={styles.resendText}>Didn't receive the email?</Text>
          <TouchableOpacity
            style={[styles.resendButton, !canResend && styles.buttonDisabled]}
            onPress={handleResendEmail}
            disabled={!canResend || isResending}
          >
            <Text style={[styles.resendButtonText, !canResend && styles.textDisabled]}>
              {isResending
                ? '📤 Sending...'
                : canResend
                ? '🔄 Resend Email'
                : `⏰ Resend in ${countdown}s`}
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.helpContainer}>
          <TouchableOpacity onPress={handleChangeEmail}>
            <Text style={styles.helpText}>
              Wrong email? <Text style={styles.helpLink}>Change it</Text>
            </Text>
          </TouchableOpacity>
        </View>

        {error && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
          </View>
        )}
      </View>

      <View style={styles.footer}>
        <TouchableOpacity onPress={handleBackToLogin}>
          <Text style={styles.backToLoginText}>
            Already verified? <Text style={styles.backToLoginLink}>Sign In</Text>
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: lightTheme.colors.background,
  },
  content: {
    flex: 1,
    paddingHorizontal: lightTheme.spacing.lg,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    paddingVertical: lightTheme.spacing.xl,
    paddingHorizontal: lightTheme.spacing.lg,
    borderRadius: lightTheme.borderRadius.xxl,
    marginHorizontal: lightTheme.spacing.md,
    marginBottom: lightTheme.spacing.xl,
    ...lightTheme.shadows.large,
  },
  heroIcon: {
    backgroundColor: lightTheme.colors.info + '20',
    borderRadius: lightTheme.borderRadius.round,
    width: 80,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: lightTheme.spacing.lg,
  },
  heroIconText: {
    fontSize: 40,
  },
  title: {
    ...lightTheme.typography.display2,
    color: lightTheme.colors.textPrimary,
    textAlign: 'center',
    marginBottom: lightTheme.spacing.md,
  },
  subtitle: {
    ...lightTheme.typography.body1,
    color: lightTheme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: lightTheme.spacing.sm,
  },
  emailText: {
    ...lightTheme.typography.h6,
    color: lightTheme.colors.primaryAccent,
    textAlign: 'center',
    fontWeight: '700',
  },
  instructionsContainer: {
    marginBottom: lightTheme.spacing.xl,
  },
  instructionsBox: {
    paddingHorizontal: lightTheme.spacing.lg,
    paddingVertical: lightTheme.spacing.lg,
    borderRadius: lightTheme.borderRadius.xl,
  },
  instructionStep: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: lightTheme.spacing.md,
  },
  stepNumber: {
    ...lightTheme.typography.h6,
    color: lightTheme.colors.info,
    fontWeight: '800',
    backgroundColor: lightTheme.colors.info + '20',
    borderRadius: lightTheme.borderRadius.round,
    width: 32,
    height: 32,
    textAlign: 'center',
    lineHeight: 32,
    marginRight: lightTheme.spacing.md,
  },
  stepText: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.info,
    flex: 1,
    fontWeight: '500',
  },
  checkButtonContainer: {
    borderRadius: lightTheme.borderRadius.xl,
    overflow: 'hidden',
    marginBottom: lightTheme.spacing.lg,
    ...lightTheme.shadows.glow,
  },
  checkButton: {
    paddingVertical: lightTheme.spacing.lg,
    alignItems: 'center',
  },
  checkButtonText: {
    ...lightTheme.typography.button,
    color: lightTheme.colors.white,
    fontSize: 18,
    fontWeight: '700',
  },
  resendContainer: {
    alignItems: 'center',
    marginBottom: lightTheme.spacing.lg,
  },
  resendText: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textSecondary,
    marginBottom: lightTheme.spacing.sm,
  },
  resendButton: {
    paddingHorizontal: lightTheme.spacing.lg,
    paddingVertical: lightTheme.spacing.md,
    borderRadius: lightTheme.borderRadius.lg,
    borderWidth: 2,
    borderColor: lightTheme.colors.primaryAccent,
  },
  buttonDisabled: {
    borderColor: lightTheme.colors.textSecondary,
    opacity: 0.6,
  },
  resendButtonText: {
    ...lightTheme.typography.buttonSmall,
    color: lightTheme.colors.primaryAccent,
    fontWeight: '600',
  },
  textDisabled: {
    color: lightTheme.colors.textSecondary,
  },
  helpContainer: {
    alignItems: 'center',
    marginBottom: lightTheme.spacing.lg,
  },
  helpText: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textSecondary,
  },
  helpLink: {
    color: lightTheme.colors.primaryAccent,
    fontWeight: '600',
  },
  errorContainer: {
    backgroundColor: lightTheme.colors.error + '20',
    paddingHorizontal: lightTheme.spacing.lg,
    paddingVertical: lightTheme.spacing.md,
    borderRadius: lightTheme.borderRadius.lg,
    marginBottom: lightTheme.spacing.lg,
    borderWidth: 1,
    borderColor: lightTheme.colors.error + '40',
  },
  errorText: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.error,
    textAlign: 'center',
    fontWeight: '600',
  },
  footer: {
    alignItems: 'center',
    paddingBottom: lightTheme.spacing.lg,
    paddingHorizontal: lightTheme.spacing.lg,
  },
  backToLoginText: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textSecondary,
    textAlign: 'center',
  },
  backToLoginLink: {
    color: lightTheme.colors.primaryAccent,
    fontWeight: '700',
  },
});