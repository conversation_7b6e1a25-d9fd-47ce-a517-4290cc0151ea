rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only read and write their own profile
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Quests are read-only for all authenticated users
    match /quests/{questId} {
      allow read: if request.auth != null;
      allow write: if false; // Only admins can modify quests (implement admin check later)
    }
    
    // Quest progress is user-specific
    match /users/{userId}/questProgress/{questId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // User badges are user-specific
    match /users/{userId}/badges/{badgeId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Public leaderboard data (read-only)
    match /leaderboard/{document} {
      allow read: if request.auth != null;
      allow write: if false; // Only cloud functions can update leaderboard
    }
    
    // Analytics data (write-only for users)
    match /analytics/{document} {
      allow write: if request.auth != null;
      allow read: if false; // Only admins can read analytics
    }
    
    // Default deny rule
    match /{document=**} {
      allow read, write: if false;
    }
  }
}