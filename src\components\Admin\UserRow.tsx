import React, { useState } from 'react';
import { View, Text, StyleSheet, Alert } from 'react-native';
import { 
  Card, 
  Avatar, 
  Chip, 
  IconButton, 
  Menu, 
  Divider,
  Button,
  Portal,
  Dialog,
  Paragraph
} from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { lightTheme } from '../../config/theme';
import { UserProfile } from '../../store/authSlice';
import { doc, updateDoc } from 'firebase/firestore';
import { db } from '../../config/firebase';
import { Ionicons } from '@expo/vector-icons';

interface UserRowProps {
  user: UserProfile;
  onUserUpdate: (updatedUser: UserProfile) => void;
}

export default function UserRow({ user, onUserUpdate }: UserRowProps) {
  const [menuVisible, setMenuVisible] = useState(false);
  const [dialogVisible, setDialogVisible] = useState(false);
  const [dialogAction, setDialogAction] = useState<'promote' | 'demote' | 'deactivate' | null>(null);
  const [loading, setLoading] = useState(false);

  const openMenu = () => setMenuVisible(true);
  const closeMenu = () => setMenuVisible(false);

  const showDialog = (action: 'promote' | 'demote' | 'deactivate') => {
    setDialogAction(action);
    setDialogVisible(true);
    closeMenu();
  };

  const hideDialog = () => {
    setDialogVisible(false);
    setDialogAction(null);
  };

  const handleUserAction = async () => {
    if (!dialogAction) return;

    setLoading(true);
    try {
      const userRef = doc(db, 'users', user.uid);
      let updates: Partial<UserProfile> = {};

      switch (dialogAction) {
        case 'promote':
          updates.role = 'admin';
          break;
        case 'demote':
          updates.role = 'user';
          break;
        case 'deactivate':
          updates.active = !user.active;
          break;
      }

      await updateDoc(userRef, updates);
      
      const updatedUser = { ...user, ...updates };
      onUserUpdate(updatedUser);
      
      Alert.alert(
        'Success',
        `User ${dialogAction === 'deactivate' ? (user.active ? 'deactivated' : 'activated') : dialogAction + 'd'} successfully!`
      );
    } catch (error) {
      console.error('Error updating user:', error);
      Alert.alert('Error', 'Failed to update user. Please try again.');
    } finally {
      setLoading(false);
      hideDialog();
    }
  };

  const getActionText = () => {
    switch (dialogAction) {
      case 'promote':
        return 'promote this user to admin';
      case 'demote':
        return 'demote this user to regular user';
      case 'deactivate':
        return user.active ? 'deactivate this user' : 'activate this user';
      default:
        return '';
    }
  };

  const getMenuOptions = () => {
    const options = [];
    
    if (user.role === 'user') {
      options.push(
        <Menu.Item
          key="promote"
          onPress={() => showDialog('promote')}
          title="Promote to Admin"
          leadingIcon="shield-plus"
        />
      );
    } else {
      options.push(
        <Menu.Item
          key="demote"
          onPress={() => showDialog('demote')}
          title="Demote to User"
          leadingIcon="shield-minus"
        />
      );
    }

    options.push(
      <Menu.Item
        key="deactivate"
        onPress={() => showDialog('deactivate')}
        title={user.active ? 'Deactivate' : 'Activate'}
        leadingIcon={user.active ? 'account-off' : 'account-check'}
      />
    );

    return options;
  };

  const getRoleBadgeColor = () => {
    return user.role === 'admin' ? lightTheme.colors.secondaryAccent : lightTheme.colors.primaryAccent;
  };

  const getStatusBadgeColor = () => {
    return user.active ? lightTheme.colors.success : lightTheme.colors.error;
  };

  return (
    <>
      <Card style={styles.userCard}>
        <Card.Content style={styles.cardContent}>
          <View style={styles.userInfo}>
            <View style={styles.avatarContainer}>
              <Avatar.Text
                size={48}
                label={user.fullName.split(' ').map(n => n[0]).join('').substring(0, 2)}
                style={[styles.avatar, { backgroundColor: getRoleBadgeColor() }]}
                labelStyle={styles.avatarLabel}
              />
              <View style={[styles.statusDot, { backgroundColor: getStatusBadgeColor() }]} />
            </View>
            
            <View style={styles.userDetails}>
              <Text style={styles.userName}>{user.fullName}</Text>
              <Text style={styles.userEmail}>{user.email}</Text>
              <View style={styles.badgeContainer}>
                <Chip
                  style={[styles.roleBadge, { backgroundColor: getRoleBadgeColor() + '20' }]}
                  textStyle={[styles.roleBadgeText, { color: getRoleBadgeColor() }]}
                  compact
                >
                  {user.role === 'admin' ? '👑 Admin' : '👤 User'}
                </Chip>
                <Chip
                  style={[styles.statusBadge, { backgroundColor: getStatusBadgeColor() + '20' }]}
                  textStyle={[styles.statusBadgeText, { color: getStatusBadgeColor() }]}
                  compact
                >
                  {user.active ? '🟢 Active' : '🔴 Inactive'}
                </Chip>
              </View>
            </View>
          </View>

          <View style={styles.userStats}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{user.level}</Text>
              <Text style={styles.statLabel}>Level</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{user.xp}</Text>
              <Text style={styles.statLabel}>XP</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{user.questsCompleted}</Text>
              <Text style={styles.statLabel}>Quests</Text>
            </View>
          </View>

          <Menu
            visible={menuVisible}
            onDismiss={closeMenu}
            anchor={
              <IconButton
                icon="dots-vertical"
                size={20}
                onPress={openMenu}
                style={styles.menuButton}
              />
            }
          >
            {getMenuOptions()}
          </Menu>
        </Card.Content>
      </Card>

      <Portal>
        <Dialog visible={dialogVisible} onDismiss={hideDialog}>
          <Dialog.Title>Confirm Action</Dialog.Title>
          <Dialog.Content>
            <Paragraph>
              Are you sure you want to {getActionText()}?
            </Paragraph>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={hideDialog}>Cancel</Button>
            <Button 
              onPress={handleUserAction}
              loading={loading}
              disabled={loading}
            >
              Confirm
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </>
  );
}

const styles = StyleSheet.create({
  userCard: {
    marginBottom: lightTheme.spacing.md,
    borderRadius: lightTheme.borderRadius.lg,
    elevation: 2,
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: lightTheme.spacing.md,
  },
  userInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: lightTheme.spacing.md,
  },
  avatar: {
    borderWidth: 2,
    borderColor: lightTheme.colors.surface,
  },
  avatarLabel: {
    color: lightTheme.colors.white,
    fontWeight: '700',
  },
  statusDot: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    width: 16,
    height: 16,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: lightTheme.colors.white,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    ...lightTheme.typography.h6,
    color: lightTheme.colors.textPrimary,
    marginBottom: lightTheme.spacing.xs,
  },
  userEmail: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textSecondary,
    marginBottom: lightTheme.spacing.sm,
  },
  badgeContainer: {
    flexDirection: 'row',
    gap: lightTheme.spacing.sm,
  },
  roleBadge: {
    height: 24,
  },
  roleBadgeText: {
    ...lightTheme.typography.caption,
    fontWeight: '600',
  },
  statusBadge: {
    height: 24,
  },
  statusBadgeText: {
    ...lightTheme.typography.caption,
    fontWeight: '600',
  },
  userStats: {
    flexDirection: 'row',
    marginRight: lightTheme.spacing.md,
  },
  statItem: {
    alignItems: 'center',
    marginHorizontal: lightTheme.spacing.sm,
  },
  statValue: {
    ...lightTheme.typography.h6,
    color: lightTheme.colors.primaryAccent,
    fontWeight: '700',
  },
  statLabel: {
    ...lightTheme.typography.caption,
    color: lightTheme.colors.textSecondary,
  },
  menuButton: {
    margin: 0,
  },
});