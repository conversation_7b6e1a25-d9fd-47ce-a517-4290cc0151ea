import { initializeApp } from 'firebase/app';
import { initializeAuth, getReactNativePersistence } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import AsyncStorage from '@react-native-async-storage/async-storage';

const firebaseConfig = {
  apiKey: "AIzaSyC7ORvxHYEFDANAu9__f7g4tROTA-dHd7Q",
  authDomain: "my-city-quest-653d8.firebaseapp.com",
  projectId: "my-city-quest-653d8",
  storageBucket: "my-city-quest-653d8.firebasestorage.app",
  messagingSenderId: "932762087438",
  appId: "1:932762087438:web:2f6d30b8920d0c367ecc5f",
  measurementId: "G-RYF6FM7ZSS"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Authentication with AsyncStorage persistence
export const auth = initializeAuth(app, {
  persistence: getReactNativePersistence(AsyncStorage)
});

// Initialize Cloud Firestore and get a reference to the service
export const db = getFirestore(app);

// Initialize Cloud Storage and get a reference to the service
export const storage = getStorage(app);

export default app;