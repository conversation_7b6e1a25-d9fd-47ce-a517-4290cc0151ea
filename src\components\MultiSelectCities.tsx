import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Modal,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';

interface MultiSelectCitiesProps {
  selectedState: string;
  selectedCities: string[];
  onCitiesChange: (cities: string[]) => void;
  style?: any;
}

const CITIES_BY_STATE: Record<string, string[]> = {
  'Maharashtra': ['Mumbai', 'Pune', 'Nagpur', 'Nashik', 'Aurangabad', 'Solapur', 'Kolhapur', 'Thane', 'Navi Mumbai', 'Amravati', 'Sangli', 'Malegaon', 'Jalgaon', 'Akola', 'Latur', 'Dhule', 'Ahmednagar', 'Chandrapur', 'Parbhani', 'Ichalkaranji'],
  'Karnataka': ['Bangalore', 'Mysore', '<PERSON><PERSON><PERSON>', 'Mangalore', 'Belgau<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>ija<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>adag', 'Udupi', 'Robertson Pet', 'Bhadravati', 'Chitradurga'],
  'Tamil Nadu': ['Chennai', 'Coimbatore', 'Madurai', 'Tiruchirappalli', 'Salem', 'Tirunelveli', 'Erode', 'Vellore', 'Thoothukudi', 'Dindigul', 'Thanjavur', 'Ranipet', 'Sivakasi', 'Karur', 'Udhagamandalam', 'Hosur', 'Nagercoil', 'Kanchipuram', 'Kumbakonam', 'Avadi'],
  'Uttar Pradesh': ['Lucknow', 'Kanpur', 'Ghaziabad', 'Agra', 'Varanasi', 'Meerut', 'Allahabad', 'Bareilly', 'Aligarh', 'Moradabad', 'Saharanpur', 'Gorakhpur', 'Noida', 'Firozabad', 'Jhansi', 'Muzaffarnagar', 'Mathura', 'Rampur', 'Shahjahanpur', 'Farrukhabad'],
  'Gujarat': ['Ahmedabad', 'Surat', 'Vadodara', 'Rajkot', 'Bhavnagar', 'Jamnagar', 'Junagadh', 'Gandhinagar', 'Nadiad', 'Morbi', 'Surendranagar', 'Bharuch', 'Vapi', 'Navsari', 'Veraval', 'Porbandar', 'Godhra', 'Bhuj', 'Anand', 'Modasa'],
  'West Bengal': ['Kolkata', 'Howrah', 'Durgapur', 'Asansol', 'Siliguri', 'Darjeeling', 'Kharagpur', 'Haldia', 'Malda', 'Baharampur', 'Habra', 'Ranaghat', 'Bankura', 'Medinipur', 'Purulia', 'Balurghat', 'Cooch Behar', 'Jalpaiguri', 'Bardhaman', 'Krishnanagar'],
  'Rajasthan': ['Jaipur', 'Jodhpur', 'Udaipur', 'Kota', 'Bikaner', 'Ajmer', 'Bharatpur', 'Alwar', 'Sikar', 'Bhilwara', 'Sri Ganganagar', 'Pali', 'Kishangarh', 'Baran', 'Dhaulpur', 'Tonk', 'Beawar', 'Hanumangarh'],
  'Madhya Pradesh': ['Bhopal', 'Indore', 'Gwalior', 'Jabalpur', 'Ujjain', 'Sagar', 'Dewas', 'Satna', 'Ratlam', 'Rewa', 'Murwara', 'Singrauli', 'Burhanpur', 'Khandwa', 'Bhind', 'Chhindwara', 'Guna', 'Shivpuri', 'Vidisha', 'Chhatarpur'],
  'Andhra Pradesh': ['Visakhapatnam', 'Vijayawada', 'Guntur', 'Nellore', 'Kurnool', 'Rajahmundry', 'Tirupati', 'Kakinada', 'Anantapur', 'Vizianagaram', 'Eluru', 'Ongole', 'Nandyal', 'Machilipatnam', 'Adoni', 'Tenali', 'Chittoor', 'Hindupur', 'Proddatur', 'Bhimavaram'],
  'Telangana': ['Hyderabad', 'Warangal', 'Nizamabad', 'Karimnagar', 'Ramagundam', 'Khammam', 'Mahbubnagar', 'Nalgonda', 'Adilabad', 'Suryapet', 'Siddipet', 'Miryalaguda', 'Jagtial', 'Mancherial', 'Nirmal', 'Kothagudem', 'Bodhan', 'Sangareddy', 'Metpally', 'Zaheerabad'],
  'Kerala': ['Thiruvananthapuram', 'Kochi', 'Kozhikode', 'Thrissur', 'Alappuzha', 'Kollam', 'Palakkad', 'Kannur', 'Kottayam', 'Malappuram', 'Thalassery', 'Ponnani', 'Vatakara', 'Kanhangad', 'Payyanur', 'Koyilandy', 'Parappanangadi', 'Kalamassery', 'Neyyattinkara'],
  'Punjab': ['Ludhiana', 'Amritsar', 'Jalandhar', 'Patiala', 'Bathinda', 'Mohali', 'Pathankot', 'Hoshiarpur', 'Batala', 'Moga', 'Abohar', 'Malerkotla', 'Khanna', 'Phagwara', 'Muktsar', 'Barnala', 'Rajpura', 'Firozpur', 'Kapurthala', 'Zirakpur'],
  'Haryana': ['Gurgaon', 'Faridabad', 'Panipat', 'Ambala', 'Yamunanagar', 'Rohtak', 'Hisar', 'Karnal', 'Sonipat', 'Panchkula', 'Bhiwani', 'Sirsa', 'Bahadurgarh', 'Jind', 'Thanesar', 'Kaithal', 'Palwal', 'Rewari', 'Hansi', 'Narnaul'],
  'Bihar': ['Patna', 'Gaya', 'Bhagalpur', 'Muzaffarpur', 'Purnia', 'Darbhanga', 'Bihar Sharif', 'Arrah', 'Begusarai', 'Katihar', 'Munger', 'Chhapra', 'Danapur', 'Saharsa', 'Hajipur', 'Sasaram', 'Dehri', 'Siwan', 'Motihari', 'Nawada'],
  'Odisha': ['Bhubaneswar', 'Cuttack', 'Rourkela', 'Brahmapur', 'Sambalpur', 'Puri', 'Balasore', 'Baripada', 'Bhadrak', 'Jharsuguda', 'Jeypore', 'Barbil', 'Kendujhar', 'Sunabeda', 'Rayagada', 'Paradip', 'Bhawanipatna', 'Dhenkanal', 'Kendrapara', 'Jagatsinghpur'],
  'Jharkhand': ['Ranchi', 'Jamshedpur', 'Dhanbad', 'Bokaro', 'Deoghar', 'Phusro', 'Hazaribagh', 'Giridih', 'Ramgarh', 'Medininagar', 'Chirkunda', 'Chaibasa', 'Gumla', 'Dumka', 'Madhupur', 'Chatra', 'Daltonganj', 'Jhumri Telaiya', 'Mihijam', 'Pakaur'],
  'Assam': ['Guwahati', 'Silchar', 'Dibrugarh', 'Jorhat', 'Nagaon', 'Tinsukia', 'Tezpur', 'Diphu', 'Dhubri', 'North Lakhimpur', 'Karimganj', 'Sibsagar', 'Goalpara', 'Barpeta', 'Mangaldoi', 'Nalbari', 'Rangia', 'Margherita', 'Hailakandi', 'Morigaon'],
  'Chhattisgarh': ['Raipur', 'Bhilai', 'Bilaspur', 'Korba', 'Durg', 'Raigarh', 'Rajnandgaon', 'Jagdalpur', 'Ambikapur', 'Mahasamund', 'Chirmiri', 'Dhamtari', 'Kanker', 'Champa', 'Janjgir', 'Sakti', 'Tilda Newra', 'Mungeli', 'Sirpur', 'Baloda Bazar'],
  'Uttarakhand': ['Dehradun', 'Haridwar', 'Roorkee', 'Haldwani', 'Rudrapur', 'Kashipur', 'Rishikesh', 'Kotdwar', 'Ramnagar', 'Pithoragarh', 'Jaspur', 'Manglaur', 'Nainital', 'Mussoorie', 'Tehri', 'Pauri', 'Srinagar', 'Almora', 'Bageshwar', 'Champawat'],
  'Himachal Pradesh': ['Shimla', 'Dharamshala', 'Solan', 'Mandi', 'Palampur', 'Baddi', 'Nahan', 'Paonta Sahib', 'Sundarnagar', 'Chamba', 'Una', 'Kullu', 'Hamirpur', 'Bilaspur', 'Kangra', 'Santokhgarh', 'Mehatpur', 'Shamshi', 'Parwanoo', 'Manali'],
  'Jammu and Kashmir': ['Srinagar', 'Jammu', 'Baramulla', 'Anantnag', 'Sopore', 'Kathua', 'Udhampur', 'Poonch', 'Rajouri', 'Punch', 'Kulgam', 'Handwara', 'Kupwara', 'Budgam', 'Ganderbal', 'Bandipora', 'Pulwama', 'Shopian', 'Doda', 'Kishtwar'],
  'Goa': ['Panaji', 'Margao', 'Vasco da Gama', 'Mapusa', 'Ponda', 'Bicholim', 'Curchorem', 'Sanquelim', 'Cuncolim', 'Canacona', 'Quepem', 'Sanguem', 'Pernem', 'Sattari', 'Valpoi', 'Aldona', 'Cortalim', 'Chinchinim', 'Saligao', 'Arambol'],
  'Tripura': ['Agartala', 'Dharmanagar', 'Udaipur', 'Kailashahar', 'Belonia', 'Khowai', 'Pratapgarh', 'Ranirbazar', 'Sonamura', 'Amarpur', 'Kumarghat', 'Teliamura', 'Sabroom', 'Manu', 'Longtharai Valley', 'Jirania', 'Mohanpur', 'Melaghar', 'Bishalgarh', 'Kamalpur'],
  'Manipur': ['Imphal', 'Thoubal', 'Bishnupur', 'Churachandpur', 'Kakching', 'Ukhrul', 'Senapati', 'Tamenglong', 'Jiribam', 'Chandel', 'Pherzawl', 'Noney', 'Tengnoupal', 'Kamjong', 'Kangpokpi', 'Moreh', 'Mayang Imphal', 'Yairipok', 'Wangjing', 'Sugnu'],
  'Meghalaya': ['Shillong', 'Tura', 'Jowai', 'Nongstoin', 'Baghmara', 'Ampati', 'Resubelpara', 'Williamnagar', 'Mairang', 'Nongpoh', 'Cherrapunji', 'Dawki', 'Bholaganj', 'Umroi', 'Byrnihat', 'Sohra', 'Mawkyrwat', 'Khliehriat', 'Amlarem', 'Rongjeng'],
  'Nagaland': ['Kohima', 'Dimapur', 'Mokokchung', 'Tuensang', 'Wokha', 'Zunheboto', 'Phek', 'Kiphire', 'Longleng', 'Peren', 'Mon', 'Tseminyu', 'Jalukie', 'Tuli', 'Chümoukedima', 'Niuland', 'Chumukedima', 'Bhandari', 'Pughoboto', 'Satakha'],
  'Mizoram': ['Aizawl', 'Lunglei', 'Saiha', 'Champhai', 'Kolasib', 'Serchhip', 'Lawngtlai', 'Mamit', 'Bairabi', 'Vairengte', 'North Vanlaiphai', 'Tlabung', 'Hnahthial', 'Khawzawl', 'Saitual', 'Darlawn', 'Thenzawl', 'Zawlnuam', 'Biate', 'Reiek'],
  'Arunachal Pradesh': ['Itanagar', 'Naharlagun', 'Pasighat', 'Tezpur', 'Bomdila', 'Tawang', 'Ziro', 'Along', 'Basar', 'Daporijo', 'Yingkiong', 'Anini', 'Tezu', 'Namsai', 'Changlang', 'Khonsa', 'Longding', 'Koloriang', 'Sagalee', 'Kimin'],
  'Sikkim': ['Gangtok', 'Namchi', 'Gyalshing', 'Mangan', 'Rangpo', 'Singtam', 'Jorethang', 'Nayabazar', 'Yuksom', 'Pelling', 'Ravangla', 'Lachung', 'Lachen', 'Chungthang', 'Dzongri', 'Tsomgo', 'Nathu La', 'Goechala', 'Kanchenjunga', 'Rumtek'],
  'Delhi': ['New Delhi', 'Delhi', 'Gurgaon', 'Faridabad', 'Ghaziabad', 'Noida', 'Greater Noida', 'Dwarka', 'Rohini', 'Janakpuri', 'Lajpat Nagar', 'Karol Bagh', 'Connaught Place', 'Chandni Chowk', 'Saket', 'Vasant Kunj', 'Nehru Place', 'Laxmi Nagar', 'Pitampura', 'Rajouri Garden']
};

export default function MultiSelectCities({ selectedState, selectedCities, onCitiesChange, style }: MultiSelectCitiesProps) {
  const { theme } = useTheme();
  const [modalVisible, setModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [tempSelectedCities, setTempSelectedCities] = useState<string[]>(selectedCities);

  const styles = createStyles(theme);

  useEffect(() => {
    setTempSelectedCities(selectedCities);
  }, [selectedCities]);

  const availableCities = CITIES_BY_STATE[selectedState] || [];

  const filteredCities = availableCities.filter(city =>
    city.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const toggleCity = (city: string) => {
    const newSelection = tempSelectedCities.includes(city)
      ? tempSelectedCities.filter(c => c !== city)
      : [...tempSelectedCities, city];
    setTempSelectedCities(newSelection);
  };

  const handleSave = () => {
    onCitiesChange(tempSelectedCities);
    setModalVisible(false);
  };

  const handleCancel = () => {
    setTempSelectedCities(selectedCities);
    setModalVisible(false);
  };

  const clearAll = () => {
    setTempSelectedCities([]);
  };

  const selectAll = () => {
    setTempSelectedCities([...availableCities]);
  };

  return (
    <View style={[styles.container, style]}>
      <Text style={styles.label}>Cities *</Text>
      <TouchableOpacity
        style={styles.selector}
        onPress={() => setModalVisible(true)}
        disabled={!selectedState}
      >
        <View style={styles.selectedContainer}>
          {selectedCities.length === 0 ? (
            <Text style={styles.placeholderText}>
              {selectedState ? 'Select cities' : 'Select state first'}
            </Text>
          ) : (
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.selectedScroll}>
              {selectedCities.map((city, index) => (
                <View key={city} style={styles.selectedChip}>
                  <Text style={styles.chipText} numberOfLines={1}>{city}</Text>
                </View>
              ))}
            </ScrollView>
          )}
        </View>
        <View style={styles.selectorRight}>
          <Text style={styles.countText}>{selectedCities.length}</Text>
          <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
        </View>
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={handleCancel}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={handleCancel}>
              <Text style={styles.cancelText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Select Cities</Text>
            <TouchableOpacity onPress={handleSave}>
              <Text style={styles.saveText}>Save</Text>
            </TouchableOpacity>
          </View>

          {/* Search Bar */}
          <View style={styles.searchContainer}>
            <Ionicons name="search-outline" size={20} color={theme.colors.textSecondary} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search cities..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor={theme.colors.textSecondary}
            />
          </View>

          {/* Quick Actions */}
          <View style={styles.actionsContainer}>
            <TouchableOpacity style={styles.actionButton} onPress={selectAll}>
              <Text style={styles.actionButtonText}>Select All</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton} onPress={clearAll}>
              <Text style={styles.actionButtonText}>Clear All</Text>
            </TouchableOpacity>
          </View>

          {/* Selected Count */}
          <View style={styles.countContainer}>
            <Text style={styles.countLabel}>
              Selected: {tempSelectedCities.length} of {availableCities.length} cities
            </Text>
          </View>

          {/* Cities List */}
          <ScrollView style={styles.citiesList} showsVerticalScrollIndicator={false}>
            {filteredCities.map((city) => {
              const isSelected = tempSelectedCities.includes(city);
              return (
                <TouchableOpacity
                  key={city}
                  style={[styles.cityItem, isSelected && styles.cityItemSelected]}
                  onPress={() => toggleCity(city)}
                >
                  <Text style={[styles.cityText, isSelected && styles.cityTextSelected]}>
                    {city}
                  </Text>
                  {isSelected && (
                    <Ionicons name="checkmark" size={20} color={theme.colors.primaryAccent} />
                  )}
                </TouchableOpacity>
              );
            })}
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.textPrimary,
    marginBottom: 8,
  },
  selector: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.borderLight,
    minHeight: 56,
    paddingHorizontal: 16,
  },
  selectedContainer: {
    flex: 1,
  },
  placeholderText: {
    color: theme.colors.textSecondary,
    fontSize: 16,
  },
  selectedScroll: {
    flexGrow: 0,
  },
  selectedChip: {
    backgroundColor: theme.colors.primaryAccent + '20',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    maxWidth: 120,
  },
  chipText: {
    color: theme.colors.primaryAccent,
    fontSize: 12,
    fontWeight: '500',
  },
  selectorRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  countText: {
    backgroundColor: theme.colors.primaryAccent,
    color: theme.colors.white,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    fontSize: 12,
    fontWeight: '600',
    minWidth: 24,
    textAlign: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.borderLight,
  },
  cancelText: {
    color: theme.colors.textSecondary,
    fontSize: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.textPrimary,
  },
  saveText: {
    color: theme.colors.primaryAccent,
    fontSize: 16,
    fontWeight: '600',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 16,
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: theme.colors.borderLight,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    color: theme.colors.textPrimary,
  },
  actionsContainer: {
    flexDirection: 'row',
    marginHorizontal: 16,
    gap: 12,
  },
  actionButton: {
    flex: 1,
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    paddingVertical: 10,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.borderLight,
  },
  actionButtonText: {
    color: theme.colors.primaryAccent,
    fontSize: 14,
    fontWeight: '600',
  },
  countContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  countLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  citiesList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  cityItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.borderLight,
  },
  cityItemSelected: {
    backgroundColor: theme.colors.primaryAccent + '10',
  },
  cityText: {
    fontSize: 16,
    color: theme.colors.textPrimary,
  },
  cityTextSelected: {
    color: theme.colors.primaryAccent,
    fontWeight: '600',
  },
});