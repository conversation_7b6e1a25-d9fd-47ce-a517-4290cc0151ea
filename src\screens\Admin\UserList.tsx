import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  RefreshControl,
  TextInput,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { 
  Card, 
  Chip, 
  FAB, 
  Button,
  Portal,
  Dialog,
  Paragraph,
  RadioButton,
  Divider
} from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation } from '@react-navigation/native';
import { lightTheme } from '../../config/theme';
import { useAuth } from '../../hooks/useAuth';
import { UserProfile } from '../../store/authSlice';
import UserRow from '../../components/Admin/UserRow';
import { 
  collection, 
  query, 
  where, 
  getDocs, 
  orderBy, 
  limit, 
  startAfter,
  DocumentSnapshot
} from 'firebase/firestore';
import { db } from '../../config/firebase';
import { Ionicons } from '@expo/vector-icons';

type FilterType = 'all' | 'admins' | 'users' | 'active' | 'inactive';

export default function UserList() {
  const navigation = useNavigation();
  const { user } = useAuth();
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<FilterType>('all');
  const [fabVisible, setFabVisible] = useState(true);
  
  // Pagination
  const [lastDoc, setLastDoc] = useState<DocumentSnapshot | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const pageSize = 20;

  const fetchUsers = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
        setLastDoc(null);
        setHasMore(true);
      } else {
        setLoading(true);
      }

      const usersRef = collection(db, 'users');
      let q = query(
        usersRef,
        orderBy('createdAt', 'desc'),
        limit(pageSize)
      );

      // If not refresh and we have a lastDoc, start after it
      if (!isRefresh && lastDoc) {
        q = query(
          usersRef,
          orderBy('createdAt', 'desc'),
          startAfter(lastDoc),
          limit(pageSize)
        );
      }

      const snapshot = await getDocs(q);
      const fetchedUsers = snapshot.docs.map(doc => ({
        ...doc.data(),
        uid: doc.id,
      })) as UserProfile[];

      if (isRefresh) {
        setUsers(fetchedUsers);
      } else {
        setUsers(prev => lastDoc ? [...prev, ...fetchedUsers] : fetchedUsers);
      }

      // Update pagination state
      if (snapshot.docs.length > 0) {
        setLastDoc(snapshot.docs[snapshot.docs.length - 1]);
      }
      setHasMore(snapshot.docs.length === pageSize);

    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const loadMoreUsers = () => {
    if (hasMore && !loading) {
      fetchUsers(false);
    }
  };

  const filterUsers = () => {
    let filtered = users;

    // Apply search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(user => 
        user.fullName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.username.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply category filter
    switch (selectedFilter) {
      case 'admins':
        filtered = filtered.filter(user => user.role === 'admin');
        break;
      case 'users':
        filtered = filtered.filter(user => user.role === 'user');
        break;
      case 'active':
        filtered = filtered.filter(user => user.active);
        break;
      case 'inactive':
        filtered = filtered.filter(user => !user.active);
        break;
      default:
        // 'all' - no additional filtering
        break;
    }

    setFilteredUsers(filtered);
  };

  useEffect(() => {
    fetchUsers(true);
  }, []);

  useEffect(() => {
    filterUsers();
  }, [users, searchQuery, selectedFilter]);

  const onRefresh = () => {
    fetchUsers(true);
  };

  const handleUserUpdate = (updatedUser: UserProfile) => {
    setUsers(prevUsers => 
      prevUsers.map(user => 
        user.uid === updatedUser.uid ? updatedUser : user
      )
    );
  };

  const getFilterCount = (filter: FilterType) => {
    switch (filter) {
      case 'admins':
        return users.filter(user => user.role === 'admin').length;
      case 'users':
        return users.filter(user => user.role === 'user').length;
      case 'active':
        return users.filter(user => user.active).length;
      case 'inactive':
        return users.filter(user => !user.active).length;
      default:
        return users.length;
    }
  };

  const FilterChip = ({ filter, label, icon }: { filter: FilterType; label: string; icon: string }) => (
    <Chip
      selected={selectedFilter === filter}
      onPress={() => setSelectedFilter(filter)}
      style={[
        styles.filterChip,
        selectedFilter === filter && styles.selectedFilterChip
      ]}
      textStyle={[
        styles.filterChipText,
        selectedFilter === filter && styles.selectedFilterChipText
      ]}
      icon={icon}
    >
      {label} ({getFilterCount(filter)})
    </Chip>
  );

  const renderUser = ({ item }: { item: UserProfile }) => (
    <UserRow user={item} onUserUpdate={handleUserUpdate} />
  );

  const renderFooter = () => {
    if (!hasMore) return null;
    return (
      <View style={styles.footerContainer}>
        <Button
          mode="outlined"
          onPress={loadMoreUsers}
          loading={loading}
          disabled={loading}
        >
          Load More
        </Button>
      </View>
    );
  };

  if (user?.role !== 'admin') {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Access Denied</Text>
          <Text style={styles.errorSubText}>You don't have permission to access this area.</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={lightTheme.gradients.primary as any}
        style={styles.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <Text style={styles.headerTitle}>👥 User Management</Text>
        <Text style={styles.headerSubtitle}>Manage all platform users</Text>
      </LinearGradient>

      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Ionicons name="search" size={20} color={lightTheme.colors.textSecondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search users..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor={lightTheme.colors.textSecondary}
          />
        </View>
      </View>

      <View style={styles.filtersContainer}>
        <FlatList
          horizontal
          showsHorizontalScrollIndicator={false}
          data={[
            { filter: 'all' as FilterType, label: 'All', icon: 'people-outline' },
            { filter: 'admins' as FilterType, label: 'Admins', icon: 'shield' },
            { filter: 'users' as FilterType, label: 'Users', icon: 'person-outline' },
            { filter: 'active' as FilterType, label: 'Active', icon: 'checkmark-circle-outline' },
            { filter: 'inactive' as FilterType, label: 'Inactive', icon: 'close-circle' },
          ]}
          renderItem={({ item }) => (
            <FilterChip
              filter={item.filter}
              label={item.label}
              icon={item.icon}
            />
          )}
          keyExtractor={(item) => item.filter}
          contentContainerStyle={styles.filtersContent}
        />
      </View>

      <FlatList
        data={filteredUsers}
        renderItem={renderUser}
        keyExtractor={(item) => item.uid}
        style={styles.userList}
        contentContainerStyle={styles.userListContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListFooterComponent={renderFooter}
        onEndReached={loadMoreUsers}
        onEndReachedThreshold={0.5}
        onScroll={(e) => {
          const { contentOffset } = e.nativeEvent;
          setFabVisible(contentOffset.y < 100);
        }}
      />

      <FAB
        style={[styles.fab, { display: fabVisible ? 'flex' : 'none' }]}
        icon="plus"
        label="Create Badge"
        onPress={() => navigation.navigate('BadgeCreate' as never)}
      />

    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: lightTheme.colors.background,
  },
  header: {
    paddingVertical: lightTheme.spacing.xl,
    paddingHorizontal: lightTheme.spacing.lg,
  },
  headerTitle: {
    ...lightTheme.typography.h3,
    color: lightTheme.colors.white,
    marginBottom: lightTheme.spacing.xs,
  },
  headerSubtitle: {
    ...lightTheme.typography.body1,
    color: lightTheme.colors.white,
    opacity: 0.9,
  },
  searchContainer: {
    padding: lightTheme.spacing.lg,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: lightTheme.colors.surface,
    borderRadius: lightTheme.borderRadius.lg,
    paddingHorizontal: lightTheme.spacing.md,
    paddingVertical: lightTheme.spacing.sm,
    elevation: 2,
  },
  searchInput: {
    flex: 1,
    marginLeft: lightTheme.spacing.sm,
    ...lightTheme.typography.body1,
    color: lightTheme.colors.textPrimary,
  },
  filtersContainer: {
    paddingBottom: lightTheme.spacing.md,
  },
  filtersContent: {
    paddingHorizontal: lightTheme.spacing.lg,
  },
  filterChip: {
    marginRight: lightTheme.spacing.sm,
    backgroundColor: lightTheme.colors.surface,
  },
  selectedFilterChip: {
    backgroundColor: lightTheme.colors.primaryAccent,
  },
  filterChipText: {
    color: lightTheme.colors.textPrimary,
  },
  selectedFilterChipText: {
    color: lightTheme.colors.white,
  },
  userList: {
    flex: 1,
  },
  userListContent: {
    paddingHorizontal: lightTheme.spacing.lg,
    paddingBottom: lightTheme.spacing.xxl,
  },
  footerContainer: {
    paddingVertical: lightTheme.spacing.lg,
    alignItems: 'center',
  },
  fab: {
    position: 'absolute',
    margin: lightTheme.spacing.lg,
    right: 0,
    bottom: 0,
    backgroundColor: lightTheme.colors.secondaryAccent,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    ...lightTheme.typography.h4,
    color: lightTheme.colors.error,
    marginBottom: lightTheme.spacing.sm,
  },
  errorSubText: {
    ...lightTheme.typography.body1,
    color: lightTheme.colors.textSecondary,
  },
});