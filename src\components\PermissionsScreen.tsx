import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ScrollView,
  Modal,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import { usePermissions } from '../hooks/usePermissions';

interface PermissionsScreenProps {
  visible: boolean;
  onClose: () => void;
  onPermissionsGranted: () => void;
  requiredPermissions?: ('location' | 'camera' | 'mediaLibrary' | 'audio' | 'storage')[];
}

const PERMISSION_INFO = {
  location: {
    icon: 'location',
    title: 'Location Access',
    description: 'Required to detect your location and create location-based badges',
    reason: 'We need this to verify you\'re at the correct location when claiming badges.',
  },
  camera: {
    icon: 'camera',
    title: 'Camera Access',
    description: 'Take photos for your badges',
    reason: 'Capture amazing photos to make your badges more engaging.',
  },
  mediaLibrary: {
    icon: 'images',
    title: 'Photo Library',
    description: 'Select images from your gallery',
    reason: 'Choose existing photos to add to your badges.',
  },
  audio: {
    icon: 'mic',
    title: 'Microphone Access',
    description: 'Record audio guides for your badges',
    reason: 'Create voice recordings to provide audio guides for badge hunters.',
  },
  storage: {
    icon: 'folder',
    title: 'Storage Access',
    description: 'Save and manage your badge files',
    reason: 'Store your badge content securely on your device.',
  },
};

export default function PermissionsScreen({ 
  visible, 
  onClose, 
  onPermissionsGranted,
  requiredPermissions = ['location', 'camera', 'mediaLibrary', 'audio']
}: PermissionsScreenProps) {
  const { theme } = useTheme();
  const { permissions, loading, checkAllPermissions, requestPermission } = usePermissions();
  const [requesting, setRequesting] = useState<string | null>(null);

  const styles = createStyles(theme);

  useEffect(() => {
    if (visible) {
      checkAllPermissions();
    }
  }, [visible]);

  const allRequiredPermissionsGranted = requiredPermissions.every(
    permission => permissions[permission]
  );

  useEffect(() => {
    if (allRequiredPermissionsGranted && visible) {
      onPermissionsGranted();
    }
  }, [allRequiredPermissionsGranted, visible]);

  const handleRequestPermission = async (permissionType: keyof typeof permissions) => {
    setRequesting(permissionType);
    try {
      const granted = await requestPermission(permissionType);
      if (!granted) {
        Alert.alert(
          'Permission Denied',
          `${PERMISSION_INFO[permissionType].title} permission is required for badge creation. Please enable it in your device settings.`,
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: () => {
              // This would open device settings - implementation depends on platform
            }},
          ]
        );
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to request permission');
    } finally {
      setRequesting(null);
      await checkAllPermissions();
    }
  };

  const handleRequestAllPermissions = async () => {
    for (const permission of requiredPermissions) {
      if (!permissions[permission]) {
        await handleRequestPermission(permission);
      }
    }
  };

  const renderPermissionItem = (permissionType: keyof typeof permissions) => {
    const info = PERMISSION_INFO[permissionType];
    const isGranted = permissions[permissionType];
    const isRequesting = requesting === permissionType;

    return (
      <View key={permissionType} style={styles.permissionItem}>
        <View style={styles.permissionIcon}>
          <Ionicons 
            name={info.icon as any} 
            size={24} 
            color={isGranted ? theme.colors.success : theme.colors.primaryAccent} 
          />
        </View>
        
        <View style={styles.permissionContent}>
          <Text style={styles.permissionTitle}>{info.title}</Text>
          <Text style={styles.permissionDescription}>{info.description}</Text>
          <Text style={styles.permissionReason}>{info.reason}</Text>
        </View>
        
        <View style={styles.permissionAction}>
          {isGranted ? (
            <View style={styles.grantedBadge}>
              <Ionicons name="checkmark-circle" size={20} color={theme.colors.success} />
              <Text style={styles.grantedText}>Granted</Text>
            </View>
          ) : (
            <TouchableOpacity
              style={styles.requestButton}
              onPress={() => handleRequestPermission(permissionType)}
              disabled={isRequesting}
            >
              {isRequesting ? (
                <ActivityIndicator size="small" color={theme.colors.white} />
              ) : (
                <Text style={styles.requestButtonText}>Allow</Text>
              )}
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color={theme.colors.textPrimary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Permissions Required</Text>
          <View style={{ width: 24 }} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <LinearGradient
            colors={theme.gradients.primary as any}
            style={styles.heroSection}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <Ionicons name="shield-checkmark" size={64} color={theme.colors.white} />
            <Text style={styles.heroTitle}>Secure Badge Creation</Text>
            <Text style={styles.heroSubtitle}>
              We need a few permissions to give you the best badge creation experience
            </Text>
          </LinearGradient>

          <View style={styles.permissionsSection}>
            <Text style={styles.sectionTitle}>Required Permissions</Text>
            <Text style={styles.sectionSubtitle}>
              These permissions help us provide core functionality for creating amazing badges
            </Text>

            {requiredPermissions.map(renderPermissionItem)}
          </View>

          <View style={styles.bottomSection}>
            <TouchableOpacity
              style={[
                styles.grantAllButton,
                allRequiredPermissionsGranted && styles.grantAllButtonDisabled
              ]}
              onPress={handleRequestAllPermissions}
              disabled={allRequiredPermissionsGranted || loading}
            >
              <LinearGradient
                colors={allRequiredPermissionsGranted ? 
                  [theme.colors.success, theme.colors.success] : 
                  theme.gradients.primary as any
                }
                style={styles.grantAllButtonGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                {loading ? (
                  <ActivityIndicator size="small" color={theme.colors.white} />
                ) : (
                  <>
                    <Ionicons 
                      name={allRequiredPermissionsGranted ? "checkmark-circle" : "shield-checkmark"} 
                      size={20} 
                      color={theme.colors.white} 
                    />
                    <Text style={styles.grantAllButtonText}>
                      {allRequiredPermissionsGranted ? 
                        'All Permissions Granted!' : 
                        'Grant All Permissions'
                      }
                    </Text>
                  </>
                )}
              </LinearGradient>
            </TouchableOpacity>

            <View style={styles.privacyNote}>
              <Ionicons name="lock-closed" size={16} color={theme.colors.textSecondary} />
              <Text style={styles.privacyText}>
                Your privacy is important. We only use these permissions for badge creation features.
              </Text>
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.borderLight,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.textPrimary,
  },
  content: {
    flex: 1,
  },
  heroSection: {
    alignItems: 'center',
    padding: 32,
    margin: 16,
    borderRadius: 20,
  },
  heroTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: theme.colors.white,
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  heroSubtitle: {
    fontSize: 16,
    color: theme.colors.white,
    textAlign: 'center',
    opacity: 0.9,
    lineHeight: 22,
  },
  permissionsSection: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: theme.colors.textPrimary,
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 24,
    lineHeight: 20,
  },
  permissionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: theme.colors.borderLight,
  },
  permissionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: theme.colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  permissionContent: {
    flex: 1,
  },
  permissionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.textPrimary,
    marginBottom: 4,
  },
  permissionDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  permissionReason: {
    fontSize: 12,
    color: theme.colors.textTertiary,
    fontStyle: 'italic',
    lineHeight: 16,
  },
  permissionAction: {
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  grantedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.success + '20',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  grantedText: {
    fontSize: 12,
    fontWeight: '600',
    color: theme.colors.success,
    marginLeft: 4,
  },
  requestButton: {
    backgroundColor: theme.colors.primaryAccent,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
    minWidth: 60,
    alignItems: 'center',
  },
  requestButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.white,
  },
  bottomSection: {
    padding: 16,
  },
  grantAllButton: {
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 16,
  },
  grantAllButtonDisabled: {
    opacity: 0.8,
  },
  grantAllButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    gap: 8,
  },
  grantAllButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: theme.colors.white,
  },
  privacyNote: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: theme.colors.surface,
    padding: 16,
    borderRadius: 12,
    gap: 8,
  },
  privacyText: {
    flex: 1,
    fontSize: 12,
    color: theme.colors.textSecondary,
    lineHeight: 18,
  },
});