import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import { AudioModule, useAudioPlayer, useAudioRecorder, RecordingPresets } from 'expo-audio';
import { useTheme } from '../contexts/ThemeContext';

interface MediaUploaderProps {
  type: 'image' | 'audio';
  label: string;
  value?: string;
  onMediaSelect: (uri: string, type: string) => void;
  style?: any;
}

export default function MediaUploader({ type, label, value, onMediaSelect, style }: MediaUploaderProps) {
  const { theme } = useTheme();
  const [uploading, setUploading] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [recordingUri, setRecordingUri] = useState<string | null>(null);
  
  const audioRecorder = useAudioRecorder(RecordingPresets.HIGH_QUALITY);
  const player = useAudioPlayer(value || '');

  const styles = createStyles(theme);

  const requestPermissions = async () => {
    if (type === 'image') {
      const { status: cameraStatus } = await ImagePicker.requestCameraPermissionsAsync();
      const { status: libraryStatus } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (cameraStatus !== 'granted' || libraryStatus !== 'granted') {
        Alert.alert(
          'Permissions needed',
          'Please grant camera and photo library permissions to upload images.',
          [{ text: 'OK' }]
        );
        return false;
      }
    } else if (type === 'audio') {
      const { granted } = await AudioModule.requestRecordingPermissionsAsync();
      if (!granted) {
        Alert.alert(
          'Permission needed',
          'Please grant microphone permission to record audio.',
          [{ text: 'OK' }]
        );
        return false;
      }
    }
    return true;
  };

  const pickImage = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    Alert.alert(
      'Select Image',
      'Choose an option',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Camera', onPress: () => openCamera() },
        { text: 'Gallery', onPress: () => openGallery() },
      ]
    );
  };

  const openCamera = async () => {
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaType.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        onMediaSelect(result.assets[0].uri, 'image');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to take photo');
    }
  };

  const openGallery = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaType.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        onMediaSelect(result.assets[0].uri, 'image');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to select image');
    }
  };

  const pickAudio = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    Alert.alert(
      'Select Audio',
      'Choose an option',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Record Audio', onPress: () => recordAudio() },
        { text: 'Choose File', onPress: () => selectAudioFile() },
      ]
    );
  };

  const recordAudio = async () => {
    try {
      await audioRecorder.prepareToRecordAsync();
      audioRecorder.record();

      Alert.alert(
        'Recording',
        'Audio recording started. Tap OK when finished.',
        [
          {
            text: 'Stop Recording',
            onPress: async () => {
              await audioRecorder.stop();
              const uri = audioRecorder.uri;
              if (uri) {
                setRecordingUri(uri);
                onMediaSelect(uri, 'audio');
              }
            },
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to start recording');
    }
  };

  const selectAudioFile = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: 'audio/*',
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets[0]) {
        onMediaSelect(result.assets[0].uri, 'audio');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to select audio file');
    }
  };

  const playAudio = async () => {
    if (!value) return;

    try {
      if (isPlaying) {
        player.pause();
        setIsPlaying(false);
      } else {
        player.play();
        setIsPlaying(true);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to play audio');
    }
  };

  const removeMedia = () => {
    Alert.alert(
      'Remove Media',
      `Are you sure you want to remove this ${type}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Remove', style: 'destructive', onPress: () => onMediaSelect('', type) },
      ]
    );
  };

  const handlePress = () => {
    if (type === 'image') {
      pickImage();
    } else {
      pickAudio();
    }
  };

  const renderContent = () => {
    if (!value) {
      return (
        <View style={styles.placeholder}>
          <Ionicons 
            name={type === 'image' ? 'image-outline' : 'musical-notes-outline'} 
            size={32} 
            color={theme.colors.textSecondary} 
          />
          <Text style={styles.placeholderText}>
            {type === 'image' ? 'Add Image' : 'Add Audio'}
          </Text>
        </View>
      );
    }

    if (type === 'image') {
      return (
        <View style={styles.mediaContainer}>
          <Image source={{ uri: value }} style={styles.imagePreview} />
          <TouchableOpacity style={styles.removeButton} onPress={removeMedia}>
            <Ionicons name="close-circle" size={24} color={theme.colors.error} />
          </TouchableOpacity>
        </View>
      );
    }

    if (type === 'audio') {
      return (
        <View style={styles.mediaContainer}>
          <View style={styles.audioContainer}>
            <TouchableOpacity style={styles.playButton} onPress={playAudio}>
              <Ionicons 
                name={isPlaying ? 'pause' : 'play'} 
                size={24} 
                color={theme.colors.white} 
              />
            </TouchableOpacity>
            <Text style={styles.audioLabel}>Audio file selected</Text>
            <TouchableOpacity style={styles.removeButton} onPress={removeMedia}>
              <Ionicons name="close-circle" size={24} color={theme.colors.error} />
            </TouchableOpacity>
          </View>
        </View>
      );
    }
  };

  return (
    <View style={[styles.container, style]}>
      <Text style={styles.label}>{label}</Text>
      <TouchableOpacity 
        style={styles.uploadArea} 
        onPress={handlePress}
        disabled={uploading}
      >
        {uploading ? (
          <ActivityIndicator size="large" color={theme.colors.primaryAccent} />
        ) : (
          renderContent()
        )}
      </TouchableOpacity>
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.textPrimary,
    marginBottom: 8,
  },
  uploadArea: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: theme.colors.borderLight,
    borderStyle: 'dashed',
    minHeight: 120,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  placeholder: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  placeholderText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 8,
  },
  mediaContainer: {
    width: '100%',
    alignItems: 'center',
    position: 'relative',
  },
  imagePreview: {
    width: 100,
    height: 100,
    borderRadius: 8,
    backgroundColor: theme.colors.background,
  },
  audioContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    paddingHorizontal: 16,
  },
  playButton: {
    backgroundColor: theme.colors.primaryAccent,
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  audioLabel: {
    flex: 1,
    fontSize: 14,
    color: theme.colors.textPrimary,
    marginLeft: 16,
  },
  removeButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
  },
});