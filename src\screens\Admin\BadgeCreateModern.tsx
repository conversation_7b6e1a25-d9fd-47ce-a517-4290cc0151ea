import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  Switch,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { lightTheme } from '../../config/theme';
import { useTheme } from '../../contexts/ThemeContext';
import { useLocation } from '../../contexts/LocationContext';
import { useAuth } from '../../hooks/useAuth';
import { usePermissions } from '../../hooks/usePermissions';
import LocationPicker from '../../components/LocationPicker';
import BadgeIconPicker from '../../components/BadgeIconPicker';
import MediaUploader from '../../components/MediaUploader';
import MultiSelectCities from '../../components/MultiSelectCities';
import PermissionsScreen from '../../components/PermissionsScreen';
import { 
  createBadge, 
  updateBadge, 
  getBadgeById, 
  deleteBadge,
  type Badge 
} from '../../services/BadgeService';

// Validation schema
const badgeSchema = yup.object().shape({
  title: yup.string().required('Title is required').min(3, 'Title must be at least 3 characters'),
  clue: yup.string().required('Clue is required').min(10, 'Clue must be at least 10 characters'),
  story: yup.string().required('Story is required').min(20, 'Story must be at least 20 characters'),
  category: yup.string().required('Please select a category'),
  unlockMethod: yup.string().oneOf(['gps', 'code']).required('Please select an unlock method'),
  coords: yup.object().shape({
    lat: yup.number().required('Latitude is required').min(-90).max(90),
    lng: yup.number().required('Longitude is required').min(-180).max(180),
  }).required('Location is required'),
  code: yup.string().when('unlockMethod', {
    is: 'code',
    then: (schema) => schema.required('Secret code is required').min(4, 'Code must be at least 4 characters'),
    otherwise: (schema) => schema.notRequired(),
  }),
  icon: yup.string().required('Please select an icon'),
  cities: yup.array().of(yup.string()).min(1, 'At least one city is required'),
  difficulty: yup.string().required('Difficulty is required'),
  xp: yup.number().required('XP is required').min(1, 'XP must be at least 1'),
  state: yup.string().required('State is required'),
});

interface BadgeFormData {
  title: string;
  clue: string;
  story: string;
  category: string;
  unlockMethod: 'gps' | 'code';
  coords: { lat: number; lng: number };
  code?: string;
  icon: string;
  cities: string[];
  state: string;
  imageUrl?: string;
  audioUrl?: string;
  difficulty: string;
  xp: number;
}

const CATEGORIES = [
  'Historical',
  'Cultural',
  'Nature',
  'Adventure',
  'Food',
  'Architecture',
  'Art',
  'Sports',
  'Technology',
  'Mystery'
];

const INDIAN_STATES = [
  'Andhra Pradesh', 'Arunachal Pradesh', 'Assam', 'Bihar', 'Chhattisgarh', 'Goa',
  'Gujarat', 'Haryana', 'Himachal Pradesh', 'Jharkhand', 'Karnataka', 'Kerala',
  'Madhya Pradesh', 'Maharashtra', 'Manipur', 'Meghalaya', 'Mizoram', 'Nagaland',
  'Odisha', 'Punjab', 'Rajasthan', 'Sikkim', 'Tamil Nadu', 'Telangana', 'Tripura',
  'Uttar Pradesh', 'Uttarakhand', 'West Bengal', 'Delhi', 'Jammu and Kashmir',
  'Ladakh', 'Puducherry', 'Chandigarh', 'Dadra and Nagar Haveli and Daman and Diu',
  'Lakshadweep', 'Andaman and Nicobar Islands'
];

const DIFFICULTIES = [
  { value: 'easy', label: 'Easy', xp: 50 },
  { value: 'medium', label: 'Medium', xp: 100 },
  { value: 'hard', label: 'Hard', xp: 200 },
  { value: 'expert', label: 'Expert', xp: 300 },
];

export default function BadgeCreateModern() {
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();
  const { user } = useAuth();
  const { userLocation, isLocationLoading } = useLocation();
  const { permissions, requestPermission } = usePermissions();
  const { badgeId } = (route.params as any) || {};
  
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [showLocationPicker, setShowLocationPicker] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [selectedLocation, setSelectedLocation] = useState<any>(null);
  const [selectedCities, setSelectedCities] = useState<string[]>([]);
  const [selectedImageUri, setSelectedImageUri] = useState<string>('');
  const [selectedAudioUri, setSelectedAudioUri] = useState<string>('');
  const [showPermissions, setShowPermissions] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    watch,
    setValue,
    reset,
    getValues,
  } = useForm<BadgeFormData>({
    resolver: yupResolver(badgeSchema),
    defaultValues: {
      title: '',
      clue: '',
      story: '',
      category: '',
      unlockMethod: 'gps',
      coords: { lat: 0, lng: 0 },
      code: '',
      icon: '',
      cities: [],
      state: '',
      imageUrl: '',
      audioUrl: '',
      difficulty: 'medium',
      xp: 100,
    },
    mode: 'onChange',
  });

  const watchedValues = watch();
  const unlockMethod = watch('unlockMethod');

  // Set user's current location as default for new badges
  useEffect(() => {
    if (!badgeId && userLocation && !isLocationLoading) {
      console.log('BadgeCreate: Setting user location as default:', userLocation);
      setValue('coords', { lat: userLocation.latitude, lng: userLocation.longitude });
      setValue('state', userLocation.state);
      if (userLocation.city && !selectedCities.includes(userLocation.city)) {
        const cities = [userLocation.city];
        setSelectedCities(cities);
        setValue('cities', cities);
      }
      setSelectedLocation(userLocation);
    }
  }, [userLocation, isLocationLoading, badgeId, setValue]);

  useEffect(() => {
    if (badgeId) {
      loadBadge();
    } else {
      // Check permissions for new badge creation
      const requiredPermissions = ['location', 'camera', 'mediaLibrary', 'audio'];
      const hasAllPermissions = requiredPermissions.every(p => permissions[p as keyof typeof permissions]);
      if (!hasAllPermissions) {
        setShowPermissions(true);
      }
    }
  }, [badgeId, permissions]);

  const loadBadge = async () => {
    if (!badgeId) return;
    
    setLoading(true);
    try {
      const badge = await getBadgeById(badgeId);
      if (badge) {
        reset({
          title: badge.title,
          clue: badge.clue,
          story: badge.story,
          category: badge.category,
          unlockMethod: badge.unlockMethod,
          coords: badge.coords,
          code: badge.code || '',
          icon: badge.icon,
          cities: badge.city || [],
          state: badge.state,
          imageUrl: badge.imageUrl || '',
          audioUrl: badge.audioUrl || '',
          difficulty: badge.difficulty || 'medium',
          xp: badge.xp || 100,
        });
        setSelectedLocation({
          latitude: badge.coords.lat,
          longitude: badge.coords.lng,
          address: `${badge.city.join(', ')}, ${badge.state}`,
          city: badge.city[0],
          state: badge.state,
          country: 'India'
        });
        setSelectedCities(badge.city || []);
        setSelectedImageUri(badge.imageUrl || '');
        setSelectedAudioUri(badge.audioUrl || '');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to load badge data');
    } finally {
      setLoading(false);
    }
  };

  const validateForm = () => {
    const errors: string[] = [];
    const values = getValues();

    if (!values.title?.trim()) errors.push('Title is required');
    if (!values.clue?.trim()) errors.push('Clue is required');
    if (!values.story?.trim()) errors.push('Story is required');
    if (!values.category) errors.push('Category is required');
    if (!values.icon) errors.push('Icon is required');
    if (!values.cities || values.cities.length === 0) errors.push('At least one city is required');
    if (!values.state) errors.push('State is required');
    if (values.coords.lat === 0 && values.coords.lng === 0) errors.push('Location is required');
    if (values.unlockMethod === 'code' && !values.code?.trim()) errors.push('Secret code is required');

    setValidationErrors(errors);
    return errors.length === 0;
  };

  const onSubmit = async (data: BadgeFormData) => {
    if (!validateForm()) {
      Alert.alert('Validation Error', validationErrors.join('\n'));
      return;
    }

    setSaving(true);
    try {
      const badgeData: Omit<Badge, 'id'> = {
        ...data,
        city: data.cities,
        createdBy: user?.uid || '',
        createdAt: new Date().toISOString(),
        active: true,
        imageUrl: selectedImageUri,
        audioUrl: selectedAudioUri,
      };

      if (badgeId) {
        await updateBadge(badgeId, badgeData);
        Alert.alert('Success', 'Badge updated successfully!');
      } else {
        await createBadge(badgeData);
        Alert.alert('Success', 'Badge created successfully!');
      }

      navigation.goBack();
    } catch (error) {
      Alert.alert('Error', 'Failed to save badge. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = () => {
    if (!badgeId) return;

    Alert.alert(
      'Delete Badge',
      'Are you sure you want to delete this badge? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            setSaving(true);
            try {
              await deleteBadge(badgeId);
              Alert.alert('Success', 'Badge deleted successfully!');
              navigation.goBack();
            } catch (error) {
              Alert.alert('Error', 'Failed to delete badge.');
            } finally {
              setSaving(false);
            }
          },
        },
      ]
    );
  };

  const handleLocationSelect = (location: any) => {
    setSelectedLocation(location);
    setValue('coords', { lat: location.latitude, lng: location.longitude });
    setValue('state', location.state);
    setShowLocationPicker(false);
    
    // Auto-add the city from location to cities list if not already present
    if (location.city && !selectedCities.includes(location.city)) {
      const newCities = [...selectedCities, location.city];
      setSelectedCities(newCities);
      setValue('cities', newCities);
    }
  };

  const handleCitiesChange = (cities: string[]) => {
    setSelectedCities(cities);
    setValue('cities', cities);
  };

  const handleMediaSelect = (uri: string, type: string) => {
    if (type === 'image') {
      setSelectedImageUri(uri);
      setValue('imageUrl', uri);
    } else if (type === 'audio') {
      setSelectedAudioUri(uri);
      setValue('audioUrl', uri);
    }
  };

  const handleDifficultyChange = (difficulty: string) => {
    setValue('difficulty', difficulty);
    const difficultyData = DIFFICULTIES.find(d => d.value === difficulty);
    if (difficultyData) {
      setValue('xp', difficultyData.xp);
    }
  };

  const renderInput = (
    name: keyof BadgeFormData,
    label: string,
    placeholder: string,
    multiline = false,
    numberOfLines = 1
  ) => (
    <View style={styles.inputContainer}>
      <Text style={styles.inputLabel}>{label}</Text>
      <Controller
        control={control}
        name={name}
        render={({ field: { onChange, value } }) => (
          <TextInput
            style={[
              styles.textInput,
              multiline && styles.textInputMultiline,
              errors[name] && styles.textInputError
            ]}
            placeholder={placeholder}
            value={value?.toString() || ''}
            onChangeText={onChange}
            multiline={multiline}
            numberOfLines={numberOfLines}
            placeholderTextColor={theme.colors.textSecondary}
          />
        )}
      />
      {errors[name] && (
        <Text style={styles.errorText}>{errors[name]?.message}</Text>
      )}
    </View>
  );

  const renderDropdown = (
    name: keyof BadgeFormData,
    label: string,
    options: string[],
    placeholder: string
  ) => (
    <View style={styles.inputContainer}>
      <Text style={styles.inputLabel}>{label}</Text>
      <Controller
        control={control}
        name={name}
        render={({ field: { onChange, value } }) => (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.optionsContainer}
          >
            {options.map((option) => (
              <TouchableOpacity
                key={option}
                style={[
                  styles.optionButton,
                  value === option && styles.optionButtonSelected
                ]}
                onPress={() => onChange(option)}
              >
                <Text style={[
                  styles.optionButtonText,
                  value === option && styles.optionButtonTextSelected
                ]}>
                  {option}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        )}
      />
      {errors[name] && (
        <Text style={styles.errorText}>{errors[name]?.message}</Text>
      )}
    </View>
  );

  const renderPreview = () => (
    <Modal
      visible={showPreview}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowPreview(false)}
    >
      <SafeAreaView style={styles.previewContainer}>
        <View style={styles.previewHeader}>
          <TouchableOpacity onPress={() => setShowPreview(false)}>
            <Ionicons name="close" size={24} color={theme.colors.textPrimary} />
          </TouchableOpacity>
          <Text style={styles.previewTitle}>Badge Preview</Text>
          <View style={{ width: 24 }} />
        </View>
        
        <ScrollView style={styles.previewContent}>
          <View style={styles.previewBadge}>
            <LinearGradient
              colors={[theme.colors.primaryAccent, theme.colors.secondaryAccent]}
              style={styles.previewBadgeGradient}
            >
              <View style={styles.previewIconContainer}>
                {watchedValues.icon ? (
                  watchedValues.icon.length === 1 ? (
                    <Text style={styles.previewEmojiIcon}>{watchedValues.icon}</Text>
                  ) : (
                    <Ionicons name={watchedValues.icon as any} size={48} color="white" />
                  )
                ) : (
                  <Ionicons name="trophy" size={48} color="white" />
                )}
              </View>
              <Text style={styles.previewBadgeTitle}>{watchedValues.title || 'Badge Title'}</Text>
              <Text style={styles.previewBadgeCategory}>{watchedValues.category || 'Category'}</Text>
            </LinearGradient>
          </View>
          
          <View style={styles.previewDetails}>
            <Text style={styles.previewDetailTitle}>Clue</Text>
            <Text style={styles.previewDetailText}>{watchedValues.clue || 'Badge clue will appear here...'}</Text>
            
            <Text style={styles.previewDetailTitle}>Story</Text>
            <Text style={styles.previewDetailText}>{watchedValues.story || 'Badge story will appear here...'}</Text>
            
            <Text style={styles.previewDetailTitle}>Location</Text>
            <Text style={styles.previewDetailText}>
              {selectedLocation ? 
                `${selectedLocation.city}, ${selectedLocation.state}` : 
                'Location not selected'
              }
            </Text>
            
            <Text style={styles.previewDetailTitle}>Unlock Method</Text>
            <Text style={styles.previewDetailText}>
              {watchedValues.unlockMethod === 'gps' ? 'GPS Location' : 'Secret Code'}
            </Text>
            
            {watchedValues.unlockMethod === 'code' && watchedValues.code && (
              <>
                <Text style={styles.previewDetailTitle}>Secret Code</Text>
                <Text style={styles.previewDetailText}>{watchedValues.code}</Text>
              </>
            )}
          </View>
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );

  const styles = createStyles(theme);

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primaryAccent} />
          <Text style={styles.loadingText}>Loading badge...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.textPrimary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {badgeId ? 'Edit Badge' : 'Create Badge'}
        </Text>
        <TouchableOpacity onPress={() => setShowPreview(true)}>
          <Ionicons name="eye-outline" size={24} color={theme.colors.primaryAccent} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Basic Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📝 Basic Information</Text>
          
          {renderInput('title', 'Badge Title', 'Enter a catchy title for your badge')}
          {renderDropdown('category', 'Category', CATEGORIES, 'Select a category')}
          
          <BadgeIconPicker
            selectedIcon={watchedValues.icon}
            onIconSelect={(icon) => setValue('icon', icon)}
          />
        </View>

        {/* Content */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📖 Content</Text>
          
          {renderInput('clue', 'Clue', 'Write a hint to help users find this location', true, 3)}
          {renderInput('story', 'Story', 'Tell the story behind this place', true, 4)}
        </View>

        {/* Media */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🎨 Media</Text>
          
          <MediaUploader
            type="image"
            label="Badge Image"
            value={selectedImageUri}
            onMediaSelect={handleMediaSelect}
          />
          
          <MediaUploader
            type="audio"
            label="Audio Guide"
            value={selectedAudioUri}
            onMediaSelect={handleMediaSelect}
          />
        </View>

        {/* Difficulty & Rewards */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🎯 Difficulty & Rewards</Text>
          
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Difficulty Level</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.optionsContainer}>
              {DIFFICULTIES.map((diff) => (
                <TouchableOpacity
                  key={diff.value}
                  style={[
                    styles.difficultyButton,
                    watchedValues.difficulty === diff.value && styles.difficultyButtonSelected
                  ]}
                  onPress={() => handleDifficultyChange(diff.value)}
                >
                  <Text style={[
                    styles.difficultyLabel,
                    watchedValues.difficulty === diff.value && styles.difficultyLabelSelected
                  ]}>
                    {diff.label}
                  </Text>
                  <Text style={[
                    styles.difficultyXP,
                    watchedValues.difficulty === diff.value && styles.difficultyXPSelected
                  ]}>
                    {diff.xp} XP
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>

          {renderInput('xp', 'Custom XP (optional)', 'Enter custom XP value')}
        </View>

        {/* Location */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📍 Location</Text>
          
          <TouchableOpacity
            style={styles.locationButton}
            onPress={() => setShowLocationPicker(true)}
          >
            <View style={styles.locationButtonContent}>
              <Ionicons name="location-outline" size={24} color={theme.colors.primaryAccent} />
              <View style={styles.locationButtonText}>
                <Text style={styles.locationButtonTitle}>
                  {selectedLocation ? 'Location Selected' : 'Select Location'}
                </Text>
                <Text style={styles.locationButtonSubtitle}>
                  {selectedLocation ? 
                    `${selectedLocation.city}, ${selectedLocation.state}` :
                    'Tap to open map picker'
                  }
                </Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
            </View>
          </TouchableOpacity>

          {renderDropdown('state', 'State', INDIAN_STATES, 'Select state')}
          
          <MultiSelectCities
            selectedState={watchedValues.state}
            selectedCities={selectedCities}
            onCitiesChange={handleCitiesChange}
          />
        </View>

        {/* Unlock Method */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🔓 Unlock Method</Text>
          
          <View style={styles.unlockMethodContainer}>
            <TouchableOpacity
              style={[
                styles.unlockMethodButton,
                unlockMethod === 'gps' && styles.unlockMethodButtonActive
              ]}
              onPress={() => setValue('unlockMethod', 'gps')}
            >
              <Ionicons 
                name="location" 
                size={24} 
                color={unlockMethod === 'gps' ? 'white' : theme.colors.textSecondary} 
              />
              <Text style={[
                styles.unlockMethodText,
                unlockMethod === 'gps' && styles.unlockMethodTextActive
              ]}>
                GPS Location
              </Text>
              <Text style={[
                styles.unlockMethodSubtext,
                unlockMethod === 'gps' && styles.unlockMethodSubtextActive
              ]}>
                Users must be at the location
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.unlockMethodButton,
                unlockMethod === 'code' && styles.unlockMethodButtonActive
              ]}
              onPress={() => setValue('unlockMethod', 'code')}
            >
              <Ionicons 
                name="key" 
                size={24} 
                color={unlockMethod === 'code' ? 'white' : theme.colors.textSecondary} 
              />
              <Text style={[
                styles.unlockMethodText,
                unlockMethod === 'code' && styles.unlockMethodTextActive
              ]}>
                Secret Code
              </Text>
              <Text style={[
                styles.unlockMethodSubtext,
                unlockMethod === 'code' && styles.unlockMethodSubtextActive
              ]}>
                Users must enter a code
              </Text>
            </TouchableOpacity>
          </View>

          {unlockMethod === 'code' && renderInput('code', 'Secret Code', 'Enter the secret code')}
        </View>

        {/* Validation Status */}
        {validationErrors.length > 0 && (
          <View style={styles.validationContainer}>
            <Text style={styles.validationTitle}>⚠️ Complete these fields:</Text>
            {validationErrors.map((error, index) => (
              <Text key={index} style={styles.validationError}>• {error}</Text>
            ))}
          </View>
        )}
      </ScrollView>

      {/* Action Buttons */}
      <View style={styles.actionContainer}>
        {badgeId && (
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={handleDelete}
            disabled={saving}
          >
            <Ionicons name="trash-outline" size={20} color="white" />
            <Text style={styles.deleteButtonText}>Delete</Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={[
            styles.saveButton,
            !isValid && styles.saveButtonDisabled
          ]}
          onPress={handleSubmit(onSubmit)}
          disabled={!isValid || saving}
        >
          {saving ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <>
              <Ionicons name="checkmark" size={20} color="white" />
              <Text style={styles.saveButtonText}>
                {badgeId ? 'Update' : 'Create'} Badge
              </Text>
            </>
          )}
        </TouchableOpacity>
      </View>

      {/* Location Picker Modal */}
      <Modal
        visible={showLocationPicker}
        animationType="slide"
        presentationStyle="fullScreen"
      >
        <LocationPicker
          onLocationSelect={handleLocationSelect}
          initialLocation={selectedLocation ? {
            latitude: selectedLocation.latitude,
            longitude: selectedLocation.longitude
          } : undefined}
        />
      </Modal>

      {renderPreview()}
      
      {/* Permissions Screen */}
      <PermissionsScreen
        visible={showPermissions}
        onClose={() => setShowPermissions(false)}
        onPermissionsGranted={() => setShowPermissions(false)}
        requiredPermissions={['location', 'camera', 'mediaLibrary', 'audio']}
      />
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.borderLight,
    backgroundColor: theme.colors.surface,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.textPrimary,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.borderLight,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.textPrimary,
    marginBottom: 16,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.textPrimary,
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: theme.colors.textPrimary,
    borderWidth: 1,
    borderColor: theme.colors.borderLight,
  },
  textInputMultiline: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  textInputError: {
    borderColor: theme.colors.error,
  },
  errorText: {
    marginTop: 4,
    fontSize: 12,
    color: theme.colors.error,
  },
  optionsContainer: {
    flexDirection: 'row',
  },
  optionButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.surface,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
    borderColor: theme.colors.borderLight,
  },
  optionButtonSelected: {
    backgroundColor: theme.colors.primaryAccent,
    borderColor: theme.colors.primaryAccent,
  },
  optionButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.textPrimary,
  },
  optionButtonTextSelected: {
    color: 'white',
  },
  locationButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: theme.colors.borderLight,
    marginBottom: 16,
  },
  locationButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationButtonText: {
    flex: 1,
    marginLeft: 12,
  },
  locationButtonTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.textPrimary,
  },
  locationButtonSubtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  unlockMethodContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  unlockMethodButton: {
    flex: 1,
    padding: 16,
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.borderLight,
    alignItems: 'center',
  },
  unlockMethodButtonActive: {
    backgroundColor: theme.colors.primaryAccent,
    borderColor: theme.colors.primaryAccent,
  },
  unlockMethodText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.textPrimary,
    marginTop: 8,
  },
  unlockMethodTextActive: {
    color: 'white',
  },
  unlockMethodSubtext: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginTop: 4,
  },
  unlockMethodSubtextActive: {
    color: 'rgba(255,255,255,0.8)',
  },
  validationContainer: {
    margin: 16,
    padding: 16,
    backgroundColor: theme.colors.error + '10',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.error + '30',
  },
  validationTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.error,
    marginBottom: 8,
  },
  validationError: {
    fontSize: 12,
    color: theme.colors.error,
    marginBottom: 4,
  },
  actionContainer: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
    backgroundColor: theme.colors.surface,
    borderTopWidth: 1,
    borderTopColor: theme.colors.borderLight,
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.error,
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  deleteButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  saveButton: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.primaryAccent,
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  saveButtonDisabled: {
    backgroundColor: theme.colors.textSecondary,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  previewContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  previewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.borderLight,
  },
  previewTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.textPrimary,
  },
  previewContent: {
    flex: 1,
    padding: 16,
  },
  previewBadge: {
    alignItems: 'center',
    marginBottom: 24,
  },
  previewBadgeGradient: {
    padding: 24,
    borderRadius: 20,
    alignItems: 'center',
    minWidth: 200,
  },
  previewIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  previewEmojiIcon: {
    fontSize: 48,
  },
  previewBadgeTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: 'white',
    textAlign: 'center',
    marginBottom: 8,
  },
  previewBadgeCategory: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.8)',
    textAlign: 'center',
  },
  previewDetails: {
    gap: 16,
  },
  previewDetailTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.textPrimary,
  },
  previewDetailText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 20,
  },
  difficultyButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginRight: 12,
    minWidth: 100,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: theme.colors.borderLight,
  },
  difficultyButtonSelected: {
    backgroundColor: theme.colors.primaryAccent,
    borderColor: theme.colors.primaryAccent,
  },
  difficultyLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.textPrimary,
    marginBottom: 4,
  },
  difficultyLabelSelected: {
    color: theme.colors.white,
  },
  difficultyXP: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  difficultyXPSelected: {
    color: theme.colors.white,
  },
});