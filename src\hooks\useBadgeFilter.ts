import { useState, useMemo } from 'react';

export interface Badge {
  id: string;
  title: string;
  category: string;
  description: string;
  clue: string;
  story: string;
  location: {
    latitude: number;
    longitude: number;
    address: string;
  };
  icon: string;
  difficulty: 'easy' | 'medium' | 'hard' | 'expert';
  createdAt: any;
  isUnlocked?: boolean;
  unlockedAt?: any;
  viaHint?: boolean;
}

export interface BadgeFilterOptions {
  searchQuery: string;
  selectedCategory: string | null;
  showUnlockedOnly: boolean;
  showLockedOnly: boolean;
}

const categories = [
  'All',
  'Historical',
  'Art & Culture',
  'Food',
  'Nature',
  'Architecture',
  'Entertainment',
  'Sports',
  'Shopping',
  'Landmarks'
];

export function useBadgeFilter(badges: Badge[]) {
  const [filterOptions, setFilterOptions] = useState<BadgeFilterOptions>({
    searchQuery: '',
    selectedCategory: null,
    showUnlockedOnly: false,
    showLockedOnly: false,
  });

  const filteredBadges = useMemo(() => {
    return badges.filter(badge => {
      // Search query filter
      if (filterOptions.searchQuery) {
        const query = filterOptions.searchQuery.toLowerCase();
        const matchesSearch = 
          badge.title.toLowerCase().includes(query) ||
          badge.description.toLowerCase().includes(query) ||
          badge.category.toLowerCase().includes(query);
        
        if (!matchesSearch) return false;
      }

      // Category filter
      if (filterOptions.selectedCategory && filterOptions.selectedCategory !== 'All') {
        if (badge.category !== filterOptions.selectedCategory) return false;
      }

      // Unlock status filter
      if (filterOptions.showUnlockedOnly && !badge.isUnlocked) return false;
      if (filterOptions.showLockedOnly && badge.isUnlocked) return false;

      return true;
    });
  }, [badges, filterOptions]);

  const updateSearchQuery = (query: string) => {
    setFilterOptions(prev => ({ ...prev, searchQuery: query }));
  };

  const updateCategory = (category: string | null) => {
    setFilterOptions(prev => ({ ...prev, selectedCategory: category }));
  };

  const toggleUnlockedOnly = () => {
    setFilterOptions(prev => ({
      ...prev,
      showUnlockedOnly: !prev.showUnlockedOnly,
      showLockedOnly: false, // Clear opposite filter
    }));
  };

  const toggleLockedOnly = () => {
    setFilterOptions(prev => ({
      ...prev,
      showLockedOnly: !prev.showLockedOnly,
      showUnlockedOnly: false, // Clear opposite filter
    }));
  };

  const clearFilters = () => {
    setFilterOptions({
      searchQuery: '',
      selectedCategory: null,
      showUnlockedOnly: false,
      showLockedOnly: false,
    });
  };

  const getFilterStats = () => {
    const total = badges.length;
    const unlocked = badges.filter(b => b.isUnlocked).length;
    const locked = total - unlocked;
    const filtered = filteredBadges.length;

    return { total, unlocked, locked, filtered };
  };

  return {
    filteredBadges,
    filterOptions,
    categories,
    updateSearchQuery,
    updateCategory,
    toggleUnlockedOnly,
    toggleLockedOnly,
    clearFilters,
    getFilterStats,
  };
}