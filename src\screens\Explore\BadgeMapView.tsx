import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Dimensions,
  Linking,
  FlatList,
  RefreshControl,
} from 'react-native';
import { Card, Button } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import * as Location from 'expo-location';
import MapFallback from '../../components/MapFallback';
import { lightTheme } from '../../config/theme';
import { Badge } from '../../hooks/useBadgeFilter';
import { collection, getDocs, doc, getDoc } from 'firebase/firestore';
import { db } from '../../config/firebase';
import { useAuth } from '../../hooks/useAuth';

const { width, height } = Dimensions.get('window');

interface MarkerData extends Badge {
  location: {
    latitude: number;
    longitude: number;
    address: string;
  };
}

export default function BadgeMapView() {
  const [badges, setBadges] = useState<MarkerData[]>([]);
  const [selectedBadge, setSelectedBadge] = useState<MarkerData | null>(null);
  const [userLocation, setUserLocation] = useState<Location.LocationObject | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const { user } = useAuth();
  const navigation = useNavigation();

  useEffect(() => {
    loadBadges();
    getUserLocation();
  }, []);

  const getUserLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission denied', 'Location permission is required to show your position on the map.');
        return;
      }

      const location = await Location.getCurrentPositionAsync({});
      setUserLocation(location);
      
      // Location obtained successfully
    } catch (error) {
      console.error('Error getting location:', error);
    }
  };

  const loadBadges = async () => {
    try {
      setLoading(true);
      
      // Load all badges
      const badgesSnapshot = await getDocs(collection(db, 'badges'));
      const badgesList: MarkerData[] = [];

      for (const badgeDoc of badgesSnapshot.docs) {
        const badgeData = { id: badgeDoc.id, ...badgeDoc.data() } as MarkerData;
        
        // Check if user has unlocked this badge
        if (user) {
          const unlockedDoc = await getDoc(
            doc(db, `users/${user.uid}/unlocked/${badgeDoc.id}`)
          );
          
          if (unlockedDoc.exists()) {
            const unlockedData = unlockedDoc.data();
            badgeData.isUnlocked = true;
            badgeData.unlockedAt = unlockedData.unlockedAt;
            badgeData.viaHint = unlockedData.viaHint || false;
          } else {
            badgeData.isUnlocked = false;
          }
        }
        
        badgesList.push(badgeData);
      }
      
      setBadges(badgesList);
    } catch (error) {
      console.error('Error loading badges:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBadgePress = (badge: MarkerData) => {
    setSelectedBadge(badge);
  };

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    loadBadges().finally(() => setRefreshing(false));
  }, []);

  const handleGoThere = (badge: MarkerData) => {
    Alert.alert(
      'Navigate to Badge',
      'Would you like to open this location in your maps app or view badge details?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Open Maps', 
          onPress: () => {
            const url = `https://maps.google.com/?q=${badge.location.latitude},${badge.location.longitude}`;
            Linking.openURL(url);
          }
        },
        { 
          text: 'View Details', 
          onPress: () => {
            navigation.navigate('BadgeDetail' as never, { badgeId: badge.id } as never);
          }
        }
      ]
    );
  };

  const renderBadgeItem = ({ item: badge }: { item: MarkerData }) => {
    const isUnlocked = badge.isUnlocked;
    const distance = userLocation ? 
      getDistance(
        { latitude: userLocation.coords.latitude, longitude: userLocation.coords.longitude },
        { latitude: badge.location.latitude, longitude: badge.location.longitude }
      ) : null;
    
    return (
      <TouchableOpacity
        style={[
          styles.badgeItem,
          isUnlocked ? styles.badgeUnlocked : styles.badgeLocked
        ]}
        onPress={() => handleBadgePress(badge)}
      >
        <View style={styles.badgeIcon}>
          <LinearGradient
            colors={isUnlocked ? 
              ['#FFD700', '#FFA500'] : // Golden glow for unlocked
              [lightTheme.colors.textSecondary, lightTheme.colors.borderLight] // Grey for locked
            }
            style={styles.badgeIconGradient}
          >
            <Text style={styles.badgeIconText}>{badge.icon}</Text>
          </LinearGradient>
        </View>
        
        <View style={styles.badgeInfo}>
          <Text style={styles.badgeTitle} numberOfLines={1}>{badge.title}</Text>
          <Text style={styles.badgeCategory}>{badge.category}</Text>
          <Text style={styles.badgeClue} numberOfLines={2}>
            {isUnlocked ? badge.description : badge.clue}
          </Text>
          {distance && (
            <Text style={styles.badgeDistance}>
              📍 {(distance / 1000).toFixed(1)}km away
            </Text>
          )}
        </View>
        
        <TouchableOpacity
          style={styles.badgeAction}
          onPress={() => handleGoThere(badge)}
        >
          <Ionicons 
            name={isUnlocked ? "checkmark-circle-outline" : "location-outline"} 
            size={24} 
            color={isUnlocked ? "#FFD700" : lightTheme.colors.primaryAccent} 
          />
        </TouchableOpacity>
      </TouchableOpacity>
    );
  };

  const getDistance = (from: {latitude: number, longitude: number}, to: {latitude: number, longitude: number}) => {
    const R = 6371e3; // Earth's radius in metres
    const φ1 = from.latitude * Math.PI/180;
    const φ2 = to.latitude * Math.PI/180;
    const Δφ = (to.latitude-from.latitude) * Math.PI/180;
    const Δλ = (to.longitude-from.longitude) * Math.PI/180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c; // Distance in metres
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>🗺️ Badge Locations</Text>
        <Text style={styles.headerSubtitle}>
          Found {badges.length} badges • {badges.filter(b => b.isUnlocked).length} unlocked
        </Text>
        {userLocation && (
          <MapFallback
            latitude={userLocation.coords.latitude}
            longitude={userLocation.coords.longitude}
            title="Your Location"
            showOpenMaps={false}
          />
        )}
      </View>
      
      <FlatList
        data={badges}
        renderItem={renderBadgeItem}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={styles.listContainer}
        ListEmptyComponent={
          !loading ? (
            <View style={styles.emptyState}>
              <Ionicons name="map-outline" size={64} color={lightTheme.colors.textSecondary} />
              <Text style={styles.emptyText}>No badges found</Text>
            </View>
          ) : null
        }
      />
      
      {loading && (
        <View style={styles.loadingContainer}>
          <Card style={styles.loadingCard}>
            <Text style={styles.loadingText}>Loading badges...</Text>
          </Card>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: lightTheme.colors.background,
  },
  header: {
    padding: lightTheme.spacing.lg,
    backgroundColor: lightTheme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: lightTheme.colors.borderLight,
  },
  headerTitle: {
    ...lightTheme.typography.h5,
    color: lightTheme.colors.textPrimary,
    fontWeight: '700',
  },
  headerSubtitle: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textSecondary,
    marginTop: lightTheme.spacing.xs,
  },
  listContainer: {
    padding: lightTheme.spacing.md,
  },
  badgeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: lightTheme.colors.surface,
    borderRadius: lightTheme.borderRadius.lg,
    padding: lightTheme.spacing.md,
    marginBottom: lightTheme.spacing.md,
    ...lightTheme.shadows.small,
  },
  badgeUnlocked: {
    borderLeftWidth: 4,
    borderLeftColor: '#FFD700',
  },
  badgeLocked: {
    borderLeftWidth: 4,
    borderLeftColor: lightTheme.colors.borderLight,
  },
  badgeIcon: {
    marginRight: lightTheme.spacing.md,
  },
  badgeIconGradient: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: lightTheme.colors.white,
  },
  badgeIconText: {
    fontSize: 24,
  },
  badgeInfo: {
    flex: 1,
    marginRight: lightTheme.spacing.md,
  },
  badgeTitle: {
    ...lightTheme.typography.h6,
    color: lightTheme.colors.textPrimary,
    fontWeight: '600',
  },
  badgeCategory: {
    ...lightTheme.typography.caption,
    color: lightTheme.colors.textSecondary,
    marginBottom: lightTheme.spacing.xs,
  },
  badgeClue: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textSecondary,
    lineHeight: 18,
  },
  badgeDistance: {
    ...lightTheme.typography.caption,
    color: lightTheme.colors.primaryAccent,
    marginTop: lightTheme.spacing.xs,
    fontWeight: '600',
  },
  badgeAction: {
    padding: lightTheme.spacing.sm,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: lightTheme.spacing.xxl,
  },
  emptyText: {
    ...lightTheme.typography.h6,
    color: lightTheme.colors.textSecondary,
    marginTop: lightTheme.spacing.md,
  },
  loadingContainer: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -75 }, { translateY: -25 }],
  },
  loadingCard: {
    padding: lightTheme.spacing.lg,
    backgroundColor: lightTheme.colors.surface,
    borderRadius: lightTheme.borderRadius.lg,
  },
  loadingText: {
    ...lightTheme.typography.body2,
    color: lightTheme.colors.textPrimary,
  },
});